import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  TrendingUp, 
  Users, 
  Shield, 
  Target, 
  Lightbulb, 
  BarChart3,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface AnalysisStep {
  id: string;
  title: string;
  description: string;
  icon: any;
  duration: number; // in seconds
}

const analysisSteps: AnalysisStep[] = [
  {
    id: 'problem-analysis',
    title: 'Problem & Solution Analysis',
    description: 'Analyzing problem significance and solution fit',
    icon: Lightbulb,
    duration: 3
  },
  {
    id: 'market-research',
    title: 'Market Research',
    description: 'Evaluating market size and customer segments',
    icon: Users,
    duration: 4
  },
  {
    id: 'competitive-analysis',
    title: 'Competitive Landscape',
    description: 'Identifying competitors and market positioning',
    icon: Shield,
    duration: 3
  },
  {
    id: 'financial-modeling',
    title: 'Financial Projections',
    description: 'Building revenue models and unit economics',
    icon: TrendingUp,
    duration: 4
  },
  {
    id: 'risk-assessment',
    title: 'Risk Assessment',
    description: 'Evaluating risks and founder-market fit',
    icon: Target,
    duration: 3
  },
  {
    id: 'strategic-recommendations',
    title: 'Strategic Recommendations',
    description: 'Generating actionable insights and next steps',
    icon: BarChart3,
    duration: 3
  }
];

interface AnalysisLoadingStateProps {
  isVisible: boolean;
  onComplete?: () => void;
}

const AnalysisLoadingState: React.FC<AnalysisLoadingStateProps> = ({ 
  isVisible, 
  onComplete 
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [stepProgress, setStepProgress] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  const totalDuration = analysisSteps.reduce((sum, step) => sum + step.duration, 0);
  const currentStep = analysisSteps[currentStepIndex];

  useEffect(() => {
    if (!isVisible) {
      setCurrentStepIndex(0);
      setStepProgress(0);
      setCompletedSteps(new Set());
      return;
    }

    const interval = setInterval(() => {
      setStepProgress(prev => {
        const newProgress = prev + (100 / (currentStep.duration * 10));
        
        if (newProgress >= 100) {
          // Mark current step as completed
          setCompletedSteps(prev => new Set([...prev, currentStep.id]));
          
          // Move to next step
          if (currentStepIndex < analysisSteps.length - 1) {
            setCurrentStepIndex(prev => prev + 1);
            return 0;
          } else {
            // All steps completed
            setTimeout(() => {
              onComplete?.();
            }, 500);
            return 100;
          }
        }
        
        return newProgress;
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isVisible, currentStepIndex, currentStep, onComplete]);

  const overallProgress = ((currentStepIndex * 100 + stepProgress) / analysisSteps.length);

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.3 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      >
        <Card className="w-full max-w-2xl bg-[var(--card)] border-[var(--card-border)]">
          <CardContent className="p-8">
            {/* Header */}
            <div className="text-center mb-8">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="inline-block mb-4"
              >
                <Brain className="h-12 w-12 text-[var(--highlight)]" />
              </motion.div>
              <h2 className="text-2xl font-bold text-[var(--card-headline)] mb-2">
                Analyzing Your SaaS Idea
              </h2>
              <p className="text-[var(--card-paragraph)]">
                Our AI is conducting a comprehensive business analysis
              </p>
            </div>

            {/* Overall Progress */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium text-[var(--card-headline)]">
                  Overall Progress
                </span>
                <span className="text-sm text-[var(--card-paragraph)]">
                  {Math.round(overallProgress)}%
                </span>
              </div>
              <Progress value={overallProgress} className="h-3" />
            </div>

            {/* Current Step */}
            <div className="mb-6">
              <motion.div
                key={currentStep.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="flex items-center gap-4 p-4 bg-[var(--highlight)] bg-opacity-10 rounded-lg"
              >
                <div className="flex-shrink-0">
                  <currentStep.icon className="h-8 w-8 text-[var(--highlight)]" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-[var(--card-headline)] mb-1">
                    {currentStep.title}
                  </h3>
                  <p className="text-sm text-[var(--card-paragraph)]">
                    {currentStep.description}
                  </p>
                  <div className="mt-2">
                    <Progress value={stepProgress} className="h-2" />
                  </div>
                </div>
                <div className="flex-shrink-0">
                  <Loader2 className="h-6 w-6 text-[var(--highlight)] animate-spin" />
                </div>
              </motion.div>
            </div>

            {/* Steps Overview */}
            <div className="space-y-3">
              <h4 className="font-medium text-[var(--card-headline)] mb-3">
                Analysis Steps
              </h4>
              {analysisSteps.map((step, index) => {
                const isCompleted = completedSteps.has(step.id);
                const isCurrent = index === currentStepIndex;
                const isPending = index > currentStepIndex;

                return (
                  <motion.div
                    key={step.id}
                    initial={{ opacity: 0.5 }}
                    animate={{ 
                      opacity: isCompleted || isCurrent ? 1 : 0.5,
                      scale: isCurrent ? 1.02 : 1
                    }}
                    transition={{ duration: 0.2 }}
                    className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                      isCurrent 
                        ? 'bg-[var(--button-secondary)] bg-opacity-50' 
                        : isCompleted 
                        ? 'bg-green-50' 
                        : 'bg-[var(--button-secondary)] bg-opacity-20'
                    }`}
                  >
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      isCompleted 
                        ? 'bg-green-500' 
                        : isCurrent 
                        ? 'bg-[var(--highlight)]' 
                        : 'bg-[var(--secondary)]'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className="h-5 w-5 text-white" />
                      ) : isCurrent ? (
                        <Loader2 className="h-4 w-4 text-white animate-spin" />
                      ) : (
                        <step.icon className="h-4 w-4 text-white" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className={`font-medium ${
                        isCompleted 
                          ? 'text-green-700' 
                          : isCurrent 
                          ? 'text-[var(--card-headline)]' 
                          : 'text-[var(--secondary)]'
                      }`}>
                        {step.title}
                      </div>
                      <div className={`text-sm ${
                        isCompleted 
                          ? 'text-green-600' 
                          : isCurrent 
                          ? 'text-[var(--card-paragraph)]' 
                          : 'text-[var(--secondary)]'
                      }`}>
                        {step.description}
                      </div>
                    </div>
                    {isCurrent && (
                      <div className="flex-shrink-0">
                        <div className="text-xs text-[var(--card-paragraph)]">
                          {Math.round(stepProgress)}%
                        </div>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>

            {/* Tips */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 2, duration: 0.5 }}
              className="mt-6 p-4 bg-[var(--button-secondary)] bg-opacity-30 rounded-lg"
            >
              <p className="text-sm text-[var(--card-paragraph)] text-center">
                💡 <strong>Tip:</strong> The more detailed your description, the more accurate and actionable the analysis will be.
              </p>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};

export default AnalysisLoadingState;
