import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronDown, 
  ChevronRight, 
  TrendingUp, 
  Users, 
  Shield, 
  Target,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Download,
  Search,
  Filter
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import type { SaaSAnalysisResult } from '@/types/saas';
import MetricsChart from '../charts/MetricsChart';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface AnalysisDashboardProps {
  result: SaaSAnalysisResult;
  onExport?: (format: 'pdf' | 'excel') => void;
}

const AnalysisDashboard: React.FC<AnalysisDashboardProps> = ({ result, onExport }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['overview']));

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'go': return 'text-green-600 bg-green-100';
      case 'no-go': return 'text-red-600 bg-red-100';
      case 'pivot': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRecommendationIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'go': return CheckCircle;
      case 'no-go': return XCircle;
      case 'pivot': return AlertTriangle;
      default: return Target;
    }
  };

  const ScoreIndicator = ({ score, label }: { score: number; label: string }) => (
    <div className="flex items-center space-x-3">
      <div className="flex-1">
        <div className="flex justify-between mb-1">
          <span className="text-sm font-medium text-[var(--card-headline)]">{label}</span>
          <span className="text-sm text-[var(--card-paragraph)]">{score}/10</span>
        </div>
        <Progress value={score * 10} className="h-2" />
      </div>
    </div>
  );

  const SectionCard = ({ 
    id, 
    title, 
    icon: Icon, 
    children, 
    defaultExpanded = false 
  }: {
    id: string;
    title: string;
    icon: any;
    children: React.ReactNode;
    defaultExpanded?: boolean;
  }) => {
    const isExpanded = expandedSections.has(id);
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="bg-[var(--card)] border-[var(--card-border)]">
          <Collapsible open={isExpanded} onOpenChange={() => toggleSection(id)}>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-[var(--button-secondary)] hover:bg-opacity-50 transition-colors">
                <CardTitle className="flex items-center justify-between text-[var(--card-headline)]">
                  <div className="flex items-center gap-3">
                    <Icon className="h-5 w-5" />
                    {title}
                  </div>
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent className="pt-0">
                {children}
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        </Card>
      </motion.div>
    );
  };

  const RecommendationIcon = getRecommendationIcon(result.recommendation);

  return (
    <div className="space-y-6">
      {/* Header with Overall Score and Recommendation */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="bg-gradient-to-r from-[var(--highlight)] to-[var(--highlight-secondary)] text-white border-0">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">SaaS Analysis Results</h2>
                <p className="text-lg opacity-90">{result.executiveSummary}</p>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold mb-2">{result.overallScore}/100</div>
                <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full ${getRecommendationColor(result.recommendation)}`}>
                  <RecommendationIcon className="h-5 w-5" />
                  <span className="font-semibold capitalize">{result.recommendation}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Search and Export Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="flex items-center gap-2 flex-1">
          <Search className="h-4 w-4 text-[var(--secondary)]" />
          <Input
            placeholder="Search analysis sections..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => onExport?.('pdf')}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export PDF
          </Button>
          <Button
            variant="outline"
            onClick={() => onExport?.('excel')}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export Excel
          </Button>
        </div>
      </div>

      {/* Analysis Sections */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="market">Market Analysis</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="strategy">Strategy</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Problem & Solution Analysis */}
          <SectionCard id="problem-solution" title="Problem & Solution Analysis" icon={Lightbulb}>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-[var(--card-headline)] mb-2">Problem Statement</h4>
                <p className="text-[var(--card-paragraph)]">{result.problemSolution.problemStatement}</p>
                <div className="mt-2">
                  <ScoreIndicator score={result.problemSolution.problemSignificance} label="Problem Significance" />
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold text-[var(--card-headline)] mb-2">Proposed Solution</h4>
                <p className="text-[var(--card-paragraph)]">{result.problemSolution.proposedSolution}</p>
              </div>

              <div>
                <h4 className="font-semibold text-[var(--card-headline)] mb-2">Unique Value Proposition</h4>
                <p className="text-[var(--card-paragraph)]">{result.problemSolution.uniqueValueProposition}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-[var(--card-headline)] mb-2">Pain Points</h4>
                  <ul className="space-y-1">
                    {result.problemSolution.targetAudiencePainPoints.map((point, index) => (
                      <li key={index} className="flex items-center gap-2 text-[var(--card-paragraph)]">
                        <div className="w-2 h-2 bg-[var(--highlight)] rounded-full"></div>
                        {point}
                      </li>
                    ))}
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-[var(--card-headline)] mb-2">Competitive Advantages</h4>
                  <ul className="space-y-1">
                    {result.problemSolution.competitiveAdvantages.map((advantage, index) => (
                      <li key={index} className="flex items-center gap-2 text-[var(--card-paragraph)]">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        {advantage}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </SectionCard>

          {/* Market Validation */}
          <SectionCard id="market-validation" title="Market Validation" icon={Target}>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-[var(--button-secondary)] bg-opacity-50 rounded-lg">
                  <div className="text-2xl font-bold text-[var(--card-headline)]">
                    {result.marketValidation.conversionRates.landingPage}%
                  </div>
                  <div className="text-sm text-[var(--card-paragraph)]">Landing Page</div>
                </div>
                <div className="text-center p-4 bg-[var(--button-secondary)] bg-opacity-50 rounded-lg">
                  <div className="text-2xl font-bold text-[var(--card-headline)]">
                    {result.marketValidation.conversionRates.signUp}%
                  </div>
                  <div className="text-sm text-[var(--card-paragraph)]">Sign Up</div>
                </div>
                <div className="text-center p-4 bg-[var(--button-secondary)] bg-opacity-50 rounded-lg">
                  <div className="text-2xl font-bold text-[var(--card-headline)]">
                    {result.marketValidation.conversionRates.trial}%
                  </div>
                  <div className="text-sm text-[var(--card-paragraph)]">Trial</div>
                </div>
                <div className="text-center p-4 bg-[var(--button-secondary)] bg-opacity-50 rounded-lg">
                  <div className="text-2xl font-bold text-[var(--card-headline)]">
                    {result.marketValidation.conversionRates.paid}%
                  </div>
                  <div className="text-sm text-[var(--card-paragraph)]">Paid</div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-[var(--card-headline)] mb-3">User Feedback Scores</h4>
                <div className="space-y-3">
                  <ScoreIndicator score={result.marketValidation.userFeedbackScores.problemFit} label="Problem Fit" />
                  <ScoreIndicator score={result.marketValidation.userFeedbackScores.solutionFit} label="Solution Fit" />
                  <ScoreIndicator score={result.marketValidation.userFeedbackScores.usability} label="Usability" />
                  <ScoreIndicator score={result.marketValidation.userFeedbackScores.value} label="Value Perception" />
                </div>
              </div>
            </div>
          </SectionCard>
        </TabsContent>

        <TabsContent value="market" className="space-y-6">
          {/* Market Analysis */}
          <SectionCard id="market-analysis" title="Market Analysis" icon={TrendingUp}>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-[var(--card-headline)] mb-3">Total Addressable Market</h4>
                  <div className="text-3xl font-bold text-[var(--highlight)] mb-1">
                    ${(result.marketAnalysis.totalAddressableMarket.value / 1000000000).toFixed(1)}B
                  </div>
                  <div className="text-sm text-[var(--card-paragraph)]">
                    Growing at {result.marketAnalysis.totalAddressableMarket.growthRate}% annually
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-[var(--card-headline)] mb-3">Serviceable Addressable Market</h4>
                  <div className="text-3xl font-bold text-[var(--highlight)] mb-1">
                    ${(result.marketAnalysis.serviceableAddressableMarket.value / 1000000000).toFixed(1)}B
                  </div>
                  <div className="text-sm text-[var(--card-paragraph)]">
                    {result.marketAnalysis.serviceableAddressableMarket.penetrationRate}% penetration opportunity
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-[var(--card-headline)] mb-3">Customer Segments</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {result.marketAnalysis.idealCustomerSegments.map((segment, index) => (
                    <div key={index} className="p-4 border border-[var(--border)] rounded-lg">
                      <h5 className="font-medium text-[var(--card-headline)] mb-2">{segment.name}</h5>
                      <p className="text-sm text-[var(--card-paragraph)] mb-2">{segment.demographics}</p>
                      <div className="text-sm">
                        <span className="font-medium">Willingness to pay:</span> ${segment.willingness_to_pay}/month
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </SectionCard>

          {/* Competitive Landscape */}
          <SectionCard id="competitive-landscape" title="Competitive Landscape" icon={Shield}>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-[var(--card-headline)] mb-3">Key Competitors</h4>
                <div className="space-y-3">
                  {result.competitiveLandscape.competitors.map((competitor, index) => (
                    <div key={index} className="p-4 border border-[var(--border)] rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium text-[var(--card-headline)]">{competitor.name}</h5>
                        <Badge variant={competitor.type === 'direct' ? 'destructive' : 'secondary'}>
                          {competitor.type}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-[var(--card-headline)]">Market Share:</span> {competitor.marketShare}%
                        </div>
                        <div>
                          <span className="font-medium text-[var(--card-headline)]">Pricing:</span> {competitor.pricingStrategy}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </SectionCard>
        </TabsContent>

        <TabsContent value="financial" className="space-y-6">
          {/* Financial Metrics Chart */}
          <MetricsChart
            financialProjections={result.businessModel.financialProjections}
            currentMetrics={result.businessModel.keyMetrics}
            industryBenchmarks={result.businessModel.industryBenchmarks}
          />
        </TabsContent>

        <TabsContent value="strategy" className="space-y-6">
          {/* Strategic Recommendations */}
          <SectionCard id="strategic-recommendations" title="Strategic Recommendations" icon={Lightbulb}>
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <RecommendationIcon className="h-6 w-6" />
                <div>
                  <div className="font-semibold text-[var(--card-headline)]">
                    Recommendation: {result.strategicRecommendations.recommendation.toUpperCase()}
                  </div>
                  <div className="text-sm text-[var(--card-paragraph)]">
                    Confidence: {result.strategicRecommendations.confidence}/10
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-[var(--card-headline)] mb-2">Rationale</h4>
                <ul className="space-y-1">
                  {result.strategicRecommendations.rationale.map((reason, index) => (
                    <li key={index} className="flex items-start gap-2 text-[var(--card-paragraph)]">
                      <div className="w-2 h-2 bg-[var(--highlight)] rounded-full mt-2"></div>
                      {reason}
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h4 className="font-semibold text-[var(--card-headline)] mb-2">Next Steps</h4>
                <div className="space-y-2">
                  {result.strategicRecommendations.nextSteps.map((step, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 bg-[var(--button-secondary)] bg-opacity-50 rounded-lg">
                      <div className="w-6 h-6 bg-[var(--highlight)] text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </div>
                      <span className="text-[var(--card-paragraph)]">{step}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </SectionCard>

          {/* Risk Assessment */}
          <SectionCard id="risk-assessment" title="Risk Assessment" icon={AlertTriangle}>
            <div className="space-y-4">
              <div className="text-center mb-4">
                <div className="text-3xl font-bold text-[var(--card-headline)] mb-1">
                  {result.riskAssessment.overallRiskScore}/10
                </div>
                <div className="text-sm text-[var(--card-paragraph)]">Overall Risk Score</div>
              </div>

              <div>
                <h4 className="font-semibold text-[var(--card-headline)] mb-3">Key Risk Factors</h4>
                <div className="space-y-3">
                  {result.riskAssessment.riskFactors.map((risk, index) => (
                    <div key={index} className="p-4 border border-[var(--border)] rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium text-[var(--card-headline)]">{risk.category}</h5>
                        <Badge variant={risk.probability * risk.impact > 50 ? 'destructive' : 'secondary'}>
                          Risk Score: {risk.probability * risk.impact}
                        </Badge>
                      </div>
                      <p className="text-sm text-[var(--card-paragraph)] mb-2">{risk.description}</p>
                      <p className="text-sm text-[var(--secondary)]">
                        <span className="font-medium">Mitigation:</span> {risk.mitigation}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </SectionCard>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalysisDashboard;
