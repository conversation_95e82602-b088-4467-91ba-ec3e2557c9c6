import { CartesianGrid, Line, <PERSON>Chart, ResponsiveContainer, <PERSON>ltip, XAxis, YA<PERSON>s } from 'recharts';
import type { SaaSAnalysisResult } from "../types/saas";

/**
 * Visualizes key SaaS metrics. Highly accessible and styled with Tailwind.
 */
export const ChartPanel: React.FC<{ result: SaaSAnalysisResult }> = ({ result }) => {
  const { keyMetrics } = result;

  // Dummy data evolution for charts (simulate timeseries for demo)
  const series = [
    { month: "Jan", MRR: keyMetrics.mrr * 0.9, Churn: keyMetrics.churnRate * 1.1 },
    { month: "Feb", MRR: keyMetrics.mrr * 0.95, Churn: keyMetrics.churnRate },
    { month: "Mar", MRR: keyMetrics.mrr, Churn: keyMetrics.churnRate * 0.95 }
  ];

  return (
    <div aria-label="Key Metrics Visualization" className="w-full mt-4 flex flex-col md:flex-row gap-4">
      <div className="w-full md:w-1/2 bg-white dark:bg-gray-800 shadow rounded p-4">
        <h3 className="font-semibold text-lg mb-2">MRR Trend</h3>
        <ResponsiveContainer width="100%" height={180}>
          <LineChart data={series}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Line type="monotone" dataKey="MRR" stroke="#3b82f6" />
          </LineChart>
        </ResponsiveContainer>
      </div>
      <div className="w-full md:w-1/2 bg-white dark:bg-gray-800 shadow rounded p-4">
        <h3 className="font-semibold text-lg mb-2">Churn Rate Trend</h3>
        <ResponsiveContainer width="100%" height={180}>
          <LineChart data={series}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Line type="monotone" dataKey="Churn" stroke="#ef4444" />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
