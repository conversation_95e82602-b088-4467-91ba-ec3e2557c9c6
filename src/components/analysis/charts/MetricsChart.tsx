import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Bar<PERSON>hart,
  Bar,
  <PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, DollarSign, Users, Target, Zap } from 'lucide-react';
import type { FinancialProjections, FinancialMetrics } from '@/types/saas';
import { motion } from 'framer-motion';
import numeral from 'numeral';

interface MetricsChartProps {
  financialProjections: FinancialProjections;
  currentMetrics: FinancialMetrics;
  industryBenchmarks?: {
    churnRate: { value: number; percentile: number };
    cac: { value: number; percentile: number };
    ltv: { value: number; percentile: number };
    grossMargin: { value: number; percentile: number };
  };
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

const MetricsChart: React.FC<MetricsChartProps> = ({
  financialProjections,
  currentMetrics,
  industryBenchmarks
}) => {
  // Prepare data for charts
  const projectionData = [
    { year: 'Year 1', ...financialProjections.year1 },
    { year: 'Year 2', ...financialProjections.year2 },
    { year: 'Year 3', ...financialProjections.year3 },
    { year: 'Year 5', ...financialProjections.year5 }
  ];

  const revenueData = projectionData.map(item => ({
    year: item.year,
    MRR: item.mrr,
    ARR: item.arr
  }));

  const customerData = projectionData.map(item => ({
    year: item.year,
    Customers: item.customers,
    'Churn Rate': item.churnRate
  }));

  const unitEconomicsData = projectionData.map(item => ({
    year: item.year,
    CAC: item.cac,
    LTV: item.ltv,
    'LTV/CAC': item.ltv / item.cac
  }));

  const benchmarkData = industryBenchmarks ? [
    {
      metric: 'Churn Rate',
      current: currentMetrics.churnRate,
      benchmark: industryBenchmarks.churnRate.value,
      percentile: industryBenchmarks.churnRate.percentile
    },
    {
      metric: 'CAC',
      current: currentMetrics.cac,
      benchmark: industryBenchmarks.cac.value,
      percentile: industryBenchmarks.cac.percentile
    },
    {
      metric: 'LTV',
      current: currentMetrics.ltv,
      benchmark: industryBenchmarks.ltv.value,
      percentile: industryBenchmarks.ltv.percentile
    },
    {
      metric: 'Gross Margin',
      current: currentMetrics.grossMargin,
      benchmark: industryBenchmarks.grossMargin.value,
      percentile: industryBenchmarks.grossMargin.percentile
    }
  ] : [];

  const formatCurrency = (value: number) => numeral(value).format('$0,0');
  const formatPercent = (value: number) => `${value}%`;

  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    color = 'blue',
    format = 'number'
  }: {
    title: string;
    value: number;
    change?: number;
    icon: any;
    color?: string;
    format?: 'currency' | 'percent' | 'number';
  }) => {
    const formatValue = (val: number) => {
      switch (format) {
        case 'currency': return formatCurrency(val);
        case 'percent': return formatPercent(val);
        default: return numeral(val).format('0,0');
      }
    };

    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="bg-[var(--card)] border-[var(--card-border)]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-[var(--card-paragraph)] mb-1">{title}</p>
                <p className="text-2xl font-bold text-[var(--card-headline)]">
                  {formatValue(value)}
                </p>
                {change !== undefined && (
                  <div className="flex items-center mt-1">
                    {change > 0 ? (
                      <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm ${change > 0 ? 'text-green-500' : 'text-red-500'}`}>
                      {Math.abs(change)}%
                    </span>
                  </div>
                )}
              </div>
              <div className={`p-3 rounded-lg bg-${color}-100`}>
                <Icon className={`h-6 w-6 text-${color}-600`} />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Monthly Recurring Revenue"
          value={currentMetrics.mrr}
          icon={DollarSign}
          color="green"
          format="currency"
        />
        <MetricCard
          title="Annual Recurring Revenue"
          value={currentMetrics.arr}
          icon={TrendingUp}
          color="blue"
          format="currency"
        />
        <MetricCard
          title="Total Customers"
          value={currentMetrics.customers}
          icon={Users}
          color="purple"
        />
        <MetricCard
          title="Churn Rate"
          value={currentMetrics.churnRate}
          icon={Target}
          color="red"
          format="percent"
        />
      </div>

      {/* Revenue Growth Chart */}
      <Card className="bg-[var(--card)] border-[var(--card-border)]">
        <CardHeader>
          <CardTitle className="text-[var(--card-headline)] flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Revenue Growth Projections
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
              <XAxis dataKey="year" stroke="var(--card-paragraph)" />
              <YAxis stroke="var(--card-paragraph)" tickFormatter={formatCurrency} />
              <Tooltip 
                formatter={(value: number) => [formatCurrency(value), '']}
                labelStyle={{ color: 'var(--card-headline)' }}
                contentStyle={{ 
                  backgroundColor: 'var(--card)', 
                  border: '1px solid var(--card-border)',
                  borderRadius: '8px'
                }}
              />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="MRR" 
                stroke="#10b981" 
                strokeWidth={3}
                dot={{ fill: '#10b981', strokeWidth: 2, r: 6 }}
              />
              <Line 
                type="monotone" 
                dataKey="ARR" 
                stroke="#3b82f6" 
                strokeWidth={3}
                dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Customer Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-[var(--card)] border-[var(--card-border)]">
          <CardHeader>
            <CardTitle className="text-[var(--card-headline)] flex items-center gap-2">
              <Users className="h-5 w-5" />
              Customer Growth & Churn
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={customerData}>
                <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
                <XAxis dataKey="year" stroke="var(--card-paragraph)" />
                <YAxis stroke="var(--card-paragraph)" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'var(--card)', 
                    border: '1px solid var(--card-border)',
                    borderRadius: '8px'
                  }}
                />
                <Legend />
                <Bar dataKey="Customers" fill="#3b82f6" />
                <Bar dataKey="Churn Rate" fill="#ef4444" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-[var(--card)] border-[var(--card-border)]">
          <CardHeader>
            <CardTitle className="text-[var(--card-headline)] flex items-center gap-2">
              <Target className="h-5 w-5" />
              Unit Economics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <LineChart data={unitEconomicsData}>
                <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
                <XAxis dataKey="year" stroke="var(--card-paragraph)" />
                <YAxis stroke="var(--card-paragraph)" tickFormatter={formatCurrency} />
                <Tooltip 
                  formatter={(value: number, name: string) => [
                    name === 'LTV/CAC' ? `${value.toFixed(1)}x` : formatCurrency(value), 
                    name
                  ]}
                  contentStyle={{ 
                    backgroundColor: 'var(--card)', 
                    border: '1px solid var(--card-border)',
                    borderRadius: '8px'
                  }}
                />
                <Legend />
                <Line type="monotone" dataKey="CAC" stroke="#f59e0b" strokeWidth={2} />
                <Line type="monotone" dataKey="LTV" stroke="#10b981" strokeWidth={2} />
                <Line type="monotone" dataKey="LTV/CAC" stroke="#8b5cf6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Industry Benchmarks */}
      {benchmarkData.length > 0 && (
        <Card className="bg-[var(--card)] border-[var(--card-border)]">
          <CardHeader>
            <CardTitle className="text-[var(--card-headline)] flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Industry Benchmarks Comparison
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {benchmarkData.map((item, index) => (
                <div key={item.metric} className="p-4 bg-[var(--button-secondary)] bg-opacity-50 rounded-lg">
                  <h4 className="font-medium text-[var(--card-headline)] mb-2">{item.metric}</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-[var(--card-paragraph)]">Your Value:</span>
                      <span className="font-medium text-[var(--card-headline)]">
                        {item.metric.includes('Rate') || item.metric.includes('Margin') 
                          ? formatPercent(item.current)
                          : formatCurrency(item.current)
                        }
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-[var(--card-paragraph)]">Industry Avg:</span>
                      <span className="text-[var(--secondary)]">
                        {item.metric.includes('Rate') || item.metric.includes('Margin') 
                          ? formatPercent(item.benchmark)
                          : formatCurrency(item.benchmark)
                        }
                      </span>
                    </div>
                    <Badge 
                      variant={item.percentile >= 70 ? "default" : item.percentile >= 50 ? "secondary" : "destructive"}
                      className="w-full justify-center"
                    >
                      {item.percentile}th percentile
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MetricsChart;
