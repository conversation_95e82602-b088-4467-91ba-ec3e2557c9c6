import { SaaSAnalysisResult } from "@/types/saas";
import { saveAs } from "file-saver";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";

/**
 * Buttons to export, share and copy results.
 */
export const ExportButtons: React.FC<{
  result: SaaSAnalysisResult | null;
  exportRef: React.RefObject<HTMLDivElement>;
}> = ({ result, exportRef }) => {
  const handlePdf = async () => {
    if (!exportRef.current) return;
    const canvas = await html2canvas(exportRef.current);
    const imgData = canvas.toDataURL("image/png");
    const pdf = new jsPDF({
      orientation: "portrait",
      unit: "pt",
      format: "a4"
    });
    pdf.addImage(imgData, "PNG", 20, 40, 550, 400);
    pdf.save("saas_analysis.pdf");
  };

  const handleCsv = () => {
    if (!result) return;
    const keys = Object.keys(result.keyMetrics);
    const values = Object.values(result.keyMetrics);
    const csv =
      ["Metric,Value"]
        .concat(keys.map((k, i) => `${k},${values[i]}`))
        .join('\n');
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8" });
    saveAs(blob, "saas_metrics.csv");
  };

  const handleCopy = () => {
    if (!result) return;
    navigator.clipboard.writeText(JSON.stringify(result, null, 2));
  };

  return (
    <div className="flex gap-2 mt-4">
      <button aria-label="Export PDF" onClick={handlePdf}
        className="px-4 py-2 rounded bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring"
      >
        Export to PDF
      </button>
      <button aria-label="Download CSV" onClick={handleCsv}
        className="px-4 py-2 rounded bg-green-500 text-white hover:bg-green-600 focus:outline-none focus:ring"
      >
        Export CSV
      </button>
      <button aria-label="Copy Results" onClick={handleCopy}
        className="px-4 py-2 rounded bg-gray-500 text-white hover:bg-gray-600 focus:outline-none focus:ring"
      >
        Copy to Clipboard
      </button>
    </div>
  );
};
