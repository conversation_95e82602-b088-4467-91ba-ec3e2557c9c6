import { AnimatePresence, motion } from "framer-motion";
import { Dispatch, SetStateAction } from "react";

interface HelpModalProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}

const samples = [
  "Analyze a SaaS that provides video conferencing, serving SMBs and enterprises worldwide.",
  "Given a SaaS for automated accounting with multi-currency and regional compliance, assess MRR and integration suggestions.",
  "Summary and recommendations for a developer-focused API platform with freemium onboarding."
];

export const HelpModal: React.FC<HelpModalProps> = ({ open, setOpen }) => (
  <AnimatePresence>
    {open && (
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={() => setOpen(false)}
        aria-modal="true"
        role="dialog"
      >
        <motion.div
          className="bg-white dark:bg-gray-900 rounded shadow-lg max-w-lg w-full p-6"
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          onClick={e => e.stopPropagation()}
        >
          <h2 className="font-bold text-xl mb-2">Sample Prompts</h2>
          <ul className="list-disc pl-5 space-y-2 text-gray-700 dark:text-gray-200">
            {samples.map((s, i) => <li key={i}>{s}</li>)}
          </ul>
          <button onClick={() => setOpen(false)} className="mt-4 px-4 py-2 rounded bg-blue-500 text-white hover:bg-blue-600 focus:ring">
            Close
          </button>
        </motion.div>
      </motion.div>
    )}
  </AnimatePresence>
);
