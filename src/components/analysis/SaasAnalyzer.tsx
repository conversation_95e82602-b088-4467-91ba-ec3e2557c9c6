import { useGemini } from "@/hooks/useGemini";
import { exportToExcel, exportToPDF } from "@/utils/export/exportUtils";
import { validateInput } from "@/utils/inputValidation";
import { AnimatePresence, motion } from "framer-motion";
import { HelpCircle, RotateCcw } from "lucide-react";
import React, { useCallback, useRef, useState } from "react";
import { Spinner } from "../ui/Spinner";
import { Button } from "../ui/button";
import { EnhancedTextarea } from "../ui/enhanced-textarea";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";
import { HelpModal } from "./HelpModal";
import AnalysisDashboard from "./dashboard/AnalysisDashboard";
import AnalysisLoadingState from "./loading/AnalysisLoadingState";

export const SaasAnalyzer: React.FC = () => {
  const [input, setInput] = useState("");
  const [helpOpen, setHelpOpen] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const exportRef = useRef<HTMLDivElement>(null);

  const {
    loading,
    error,
    result,
    analyze,
    setResult
  } = useGemini();

  // Validate input before submission
  const validation = input.trim() ? validateInput(input) : null;
  const canSubmit = validation?.isValid && !loading && input.trim().length > 0;

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    if (!canSubmit) return;

    setIsAnalyzing(true);
    try {
      await analyze({ description: input });
    } finally {
      setIsAnalyzing(false);
    }
  }, [canSubmit, analyze, input]);

  // Handle clear with confirmation for long text
  const handleClear = useCallback(() => {
    if (input.length > 100) {
      if (!window.confirm('Are you sure you want to clear your description? This action cannot be undone.')) {
        return;
      }
    }
    setInput("");
    setResult(null);
  }, [input.length, setResult]);

  // Handle input change
  const handleInputChange = useCallback((value: string) => {
    setInput(value);
  }, []);

  // Handle export functionality
  const handleExport = useCallback(async (format: 'pdf' | 'excel') => {
    if (!result) return;

    try {
      if (format === 'pdf') {
        await exportToPDF(result, 'analysis-dashboard');
      } else {
        exportToExcel(result);
      }
    } catch (error) {
      console.error('Export failed:', error);
      // You could add a toast notification here
    }
  }, [result]);

  return (
    <TooltipProvider>
      <div className="max-w-4xl mx-auto py-8 px-4">
        {/* Help Button */}
        <div className="flex justify-end mb-6">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setHelpOpen(true)}
                className="text-[var(--secondary)] hover:text-[var(--headline)]"
              >
                <HelpCircle className="h-5 w-5 mr-2" />
                Help & Examples
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>View examples and help</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Main Form */}
        <motion.form
          onSubmit={(e) => {
            e.preventDefault();
            handleSubmit();
          }}
          className="space-y-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          {/* Enhanced Textarea */}
          <div className="space-y-2">
            <label htmlFor="saas-description" className="block text-sm font-medium text-[var(--headline)]">
              SaaS Description
            </label>
            <EnhancedTextarea
              value={input}
              onChange={handleInputChange}
              onSubmit={handleSubmit}
              disabled={loading || isAnalyzing}
              loading={loading || isAnalyzing}
              showAnalyzeButton={true}
              minRows={4}
              maxRows={12}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-center">{/* Analyze button is now inside the textarea */}

            {(input.trim() || result) && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                whileTap={{ scale: 0.98 }}
              >
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleClear}
                      disabled={loading || isAnalyzing}
                      className="bg-[var(--button-secondary)] text-[var(--button-secondary-text)] hover:bg-[var(--button-secondary-hover)] border-[var(--border)]"
                    >
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Clear
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Clear description and results</p>
                  </TooltipContent>
                </Tooltip>
              </motion.div>
            )}
          </div>
        </motion.form>

        {/* Help Modal */}
        <HelpModal open={helpOpen} setOpen={setHelpOpen} />

        {/* Error Display */}
        <AnimatePresence>
          {error && (
            <motion.div
              className="bg-[var(--danger)] text-[var(--danger-text)] mt-6 p-4 rounded-lg"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              role="alert"
            >
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-[var(--danger-text)] rounded-full"></div>
                <span className="font-medium">Analysis Error</span>
              </div>
              <p className="mt-1 text-sm">{error}</p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Loading State */}
        <AnimatePresence>
          {(loading || isAnalyzing) && !result && (
            <motion.div
              key="loading"
              initial={{ opacity: 0, scale: 0.98 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.98 }}
              className="mt-8"
            >
              <div className="bg-[var(--card)] animate-pulse rounded-lg p-6 space-y-4 border border-[var(--card-border)]">
                <div className="flex items-center gap-3">
                  <Spinner />
                  <span className="text-[var(--card-headline)] font-medium">
                    Analyzing your SaaS idea...
                  </span>
                </div>
                <div className="space-y-3">
                  <div className="h-4 bg-[var(--button-secondary)] rounded w-3/4"></div>
                  <div className="h-4 bg-[var(--button-secondary)] rounded w-1/2"></div>
                  <div className="h-4 bg-[var(--button-secondary)] rounded w-2/3"></div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Results Display */}
        <AnimatePresence>
          {result && (
            <motion.div
              ref={exportRef}
              key="result"
              className="bg-[var(--card)] mt-8 rounded-lg shadow-lg p-6 border border-[var(--card-border)]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            >
              {/* Comprehensive Analysis Dashboard */}
              <div id="analysis-dashboard">
                <AnalysisDashboard
                  result={result}
                  onExport={handleExport}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>


      </div>
    </TooltipProvider>
  );
};
