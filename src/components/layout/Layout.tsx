import React from 'react';

import CookieConsent from '@/components/common/CookieConsent';
import Navbar from '../common/Navbar';
import Footer from '../common/Footer';

interface LayoutProps {
  children: React.ReactNode;
  showHero?: boolean;
}

const Layout: React.FC<LayoutProps> = ({ children, showHero = false }) => {
  return (
    <div className="min-h-screen flex flex-col bg-[var(--background)]">
      <Navbar />
      <main className="flex-1">
        {children}
      </main>
      <Footer />
      <CookieConsent />
    </div>
  );
};

export default Layout;
