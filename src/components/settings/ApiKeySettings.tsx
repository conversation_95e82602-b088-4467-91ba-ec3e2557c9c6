import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Key, Save, Trash2, ExternalLink, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';
import { 
  getGeminiApiKey, 
  saveGeminiApiKey, 
  removeGeminiApiKey, 
  validateApiKey, 
  hasGeminiApiKey 
} from '@/utils/apiKeyStorage';

export const ApiKeySettings: React.FC = () => {
  const [apiKey, setApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [isLoading, setSaving] = useState(false);
  const [hasKey, setHasKey] = useState(false);

  useEffect(() => {
    // Check if API key exists on component mount
    setHasKey(hasGeminiApiKey());
    
    // Load existing key if available (masked for security)
    const existingKey = getGeminiApiKey();
    if (existingKey) {
      setApiKey(existingKey);
    }
  }, []);

  const handleSave = async () => {
    if (!apiKey.trim()) {
      toast.error('Please enter an API key');
      return;
    }

    if (!validateApiKey(apiKey.trim())) {
      toast.error('Invalid API key format. Google API keys should start with "AIza" and be 39 characters long.');
      return;
    }

    setSaving(true);
    try {
      saveGeminiApiKey(apiKey.trim());
      setHasKey(true);
      toast.success('API key saved successfully');
    } catch (error) {
      toast.error('Failed to save API key');
      console.error('Error saving API key:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleRemove = () => {
    if (window.confirm('Are you sure you want to remove your API key? This will prevent the app from making API calls.')) {
      try {
        removeGeminiApiKey();
        setApiKey('');
        setHasKey(false);
        toast.success('API key removed successfully');
      } catch (error) {
        toast.error('Failed to remove API key');
        console.error('Error removing API key:', error);
      }
    }
  };

  const toggleShowApiKey = () => {
    setShowApiKey(!showApiKey);
  };

  const maskApiKey = (key: string) => {
    if (key.length <= 8) return key;
    return key.substring(0, 4) + '•'.repeat(key.length - 8) + key.substring(key.length - 4);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          Gemini API Key Settings
        </CardTitle>
        <CardDescription>
          Configure your Google Gemini API key for SaaS analysis. Your key is stored securely in browser cookies.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Security Warning */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Security Notice:</strong> Your API key will be stored in browser cookies and used directly from the frontend. 
            For production use, ensure you have configured domain restrictions in Google Cloud Console.
          </AlertDescription>
        </Alert>

        {/* API Key Input */}
        <div className="space-y-2">
          <Label htmlFor="api-key">Google Gemini API Key</Label>
          <div className="relative">
            <Input
              id="api-key"
              type={showApiKey ? 'text' : 'password'}
              value={showApiKey ? apiKey : (apiKey ? maskApiKey(apiKey) : '')}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="AIza..."
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={toggleShowApiKey}
            >
              {showApiKey ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Get your API key from{' '}
            <a
              href="https://makersuite.google.com/app/apikey"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline inline-flex items-center gap-1"
            >
              Google AI Studio
              <ExternalLink className="h-3 w-3" />
            </a>
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <Button
            onClick={handleSave}
            disabled={isLoading || !apiKey.trim()}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isLoading ? 'Saving...' : 'Save API Key'}
          </Button>
          
          {hasKey && (
            <Button
              variant="destructive"
              onClick={handleRemove}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Remove Key
            </Button>
          )}
        </div>

        {/* Status */}
        <div className="pt-4 border-t">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${hasKey ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className="text-sm font-medium">
              Status: {hasKey ? 'API Key Configured' : 'No API Key'}
            </span>
          </div>
          {hasKey && (
            <p className="text-sm text-muted-foreground mt-1">
              Your API key is ready for use. The app will use this key for all Gemini API calls.
            </p>
          )}
        </div>

        {/* Security Recommendations */}
        <div className="pt-4 border-t">
          <h4 className="text-sm font-medium mb-2">Security Recommendations</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Configure domain restrictions in Google Cloud Console</li>
            <li>• Set up HTTP referrer restrictions for your domain</li>
            <li>• Monitor API usage and set billing alerts</li>
            <li>• Rotate your API key regularly</li>
            <li>• Never share your API key publicly</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
