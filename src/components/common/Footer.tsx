import { motion } from 'framer-motion';
import { ExternalLink, Github, Heart } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';
import Logo from './Logo';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = [
    { name: 'Home', href: '/', internal: true },
    { name: 'History', href: '/history', internal: true },
    { name: 'GitHub', href: 'https://github.com/balshaer/saascan', internal: false },
  ];

  return (
    <footer className="bg-[var(--card)] border-t border-[var(--border)] mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            {/* Brand Section */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <Logo
                size="md"
                animated={true}
                linkTo="/"
                className="text-[var(--card-headline)]"
              />
            </motion.div>

            {/* Navigation Links */}
            <div className="flex items-center space-x-6">
              {footerLinks.map((link) => (
                <motion.div
                  key={link.name}
                  whileHover={{ y: -2 }}
                  transition={{ duration: 0.2 }}
                >
                  {link.internal ? (
                    <Link
                      to={link.href}
                      className="text-[var(--card-paragraph)] hover:text-[var(--card-headline)] transition-colors duration-200 text-sm font-medium"
                    >
                      {link.name}
                    </Link>
                  ) : (
                    <a
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 text-[var(--card-paragraph)] hover:text-[var(--card-headline)] transition-colors duration-200 text-sm font-medium"
                    >
                      {link.name === 'GitHub' && <Github className="w-4 h-4" />}
                      {link.name}
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  )}
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-[var(--border)] py-4">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <p className="text-[var(--card-paragraph)] text-sm">
              © {currentYear} SaaScan. All rights reserved.
            </p>

            <div className="flex flex-col sm:flex-row items-center gap-2 text-sm">
              <motion.div
                className="flex items-center gap-1 text-[var(--card-paragraph)]"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <span>Made with</span>
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 3
                  }}
                >
                  <Heart className="w-4 h-4 text-red-500 fill-current" />
                </motion.div>
                <span>for entrepreneurs</span>
              </motion.div>

              <span className="text-[var(--card-paragraph)] hidden sm:inline">•</span>

              <motion.a
                href="https://ai.google.dev/gemini-api"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 text-[var(--card-paragraph)] hover:text-[var(--card-headline)] transition-colors"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <span>Powered by</span>
                <span className="font-semibold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
                  Gemini
                </span>
                <ExternalLink className="w-3 h-3" />
              </motion.a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
