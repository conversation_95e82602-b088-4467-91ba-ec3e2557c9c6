import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';
import React from 'react';
import { Link } from 'react-router-dom';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
  animated?: boolean;
  linkTo?: string;
}

const Logo: React.FC<LogoProps> = ({
  size = 'md',
  showText = true,
  className,
  animated = false,
  linkTo = '/'
}) => {
  const sizeClasses = {
    sm: {
      container: 'w-6 h-6',
      icon: 'w-4 h-4',
      text: 'text-sm'
    },
    md: {
      container: 'w-8 h-8',
      icon: 'w-5 h-5',
      text: 'text-lg'
    },
    lg: {
      container: 'w-12 h-12',
      icon: 'w-8 h-8',
      text: 'text-2xl'
    }
  };

  const currentSize = sizeClasses[size];

  const LogoIcon = () => (
    <motion.div
      className={cn(
        'flex items-center justify-center rounded-lg',
        currentSize.container
      )}
      animate={animated ? {
        scale: [1, 1.05, 1],
        rotate: [0, 2, -2, 0]
      } : {}}
      transition={animated ? {
        duration: 3,
        repeat: Infinity,
        repeatDelay: 5
      } : {}}
    >
      <img
        src="/images/logo.png"
        alt="SaaScan Logo"
        className={cn('object-contain', currentSize.container)}
      />
    </motion.div>
  );

  const LogoContent = () => {
    if (!showText) {
      return <LogoIcon />;
    }

    return (
      <div className={cn('flex items-center space-x-3', className)}>
        <LogoIcon />
        <div>
          <motion.h3
            className={cn('font-bold text-[var(--headline)]', currentSize.text)}
            animate={animated ? {
              opacity: [1, 0.8, 1]
            } : {}}
            transition={animated ? {
              duration: 2,
              repeat: Infinity,
              repeatDelay: 4
            } : {}}
          >
            SaaScan
          </motion.h3>
          {size !== 'sm' && (
            <p className="text-xs text-[var(--paragraph)]">
              AI-Powered SaaS Analysis
            </p>
          )}
        </div>
      </div>
    );
  };

  if (linkTo) {
    return (
      <Link
        to={linkTo}
        aria-label="Go to home - SaaScan"
        className="inline-block cursor-pointer select-none"
        tabIndex={0}
      >
        <LogoContent />
      </Link>
    );
  }

  return <LogoContent />;
};

export default Logo;
