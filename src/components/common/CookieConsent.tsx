import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, <PERSON>ie } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface CookieConsentProps {
  onAccept?: () => void;
  onDecline?: () => void;
}

const COOKIE_CONSENT_KEY = 'saascan_cookie_consent';

export const CookieConsent: React.FC<CookieConsentProps> = ({ onAccept, onDecline }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem(COOKIE_CONSENT_KEY);
    if (!consent) {
      // Show consent after a short delay for better UX
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem(COOKIE_CONSENT_KEY, 'accepted');
    setIsVisible(false);
    onAccept?.();
  };

  const handleDecline = () => {
    localStorage.setItem(COOKIE_CONSENT_KEY, 'declined');
    setIsVisible(false);
    onDecline?.();
  };

  const handleClose = () => {
    // Treat close as decline
    handleDecline();
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 100, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 100, scale: 0.95 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 30,
            duration: 0.5 
          }}
          className="fixed bottom-4 right-4 z-50 max-w-sm w-full mx-4 sm:mx-0"
        >
          <Card className="bg-[var(--card)] border-[var(--card-border)] shadow-lg">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-1">
                  <Cookie className="h-5 w-5 text-[var(--highlight)]" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-[var(--card-headline)] mb-1">
                    Cookie Consent
                  </h3>
                  <p className="text-xs text-[var(--card-paragraph)] mb-3 leading-relaxed">
                    We use cookies to enhance your experience and save your analysis results locally. 
                    No personal data is sent to external servers.
                  </p>
                  <div className="flex gap-2">
                    <Button
                      onClick={handleAccept}
                      size="sm"
                      className="bg-[var(--button)] text-[var(--button-text)] hover:bg-[var(--button-hover)] text-xs px-3 py-1.5 h-auto"
                    >
                      Accept
                    </Button>
                    <Button
                      onClick={handleDecline}
                      variant="outline"
                      size="sm"
                      className="bg-[var(--button-secondary)] text-[var(--button-secondary-text)] hover:bg-[var(--button-secondary-hover)] border-[var(--border)] text-xs px-3 py-1.5 h-auto"
                    >
                      Decline
                    </Button>
                  </div>
                </div>
                <button
                  onClick={handleClose}
                  className="flex-shrink-0 text-[var(--secondary)] hover:text-[var(--headline)] transition-colors"
                  aria-label="Close cookie consent"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Hook to check if cookies are accepted
export const useCookieConsent = () => {
  const [consent, setConsent] = useState<string | null>(null);

  useEffect(() => {
    const storedConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
    setConsent(storedConsent);
  }, []);

  const isAccepted = consent === 'accepted';
  const isDeclined = consent === 'declined';
  const hasChoiceMade = consent !== null;

  return {
    isAccepted,
    isDeclined,
    hasChoiceMade,
    consent
  };
};

export default CookieConsent;
