import { useAutoSave } from '@/hooks/useAutoSave';
import { useRotatingPlaceholder } from '@/hooks/useRotatingPlaceholder';
import { cn } from '@/lib/utils';
import { getCharacterInfo, validateInput, ValidationResult } from '@/utils/inputValidation';
import { AnimatePresence, motion } from 'framer-motion';
import { AlertCircle, CheckCircle, Info, Lightbulb, Loader2, Save, Send } from 'lucide-react';
import React, { useCallback, useEffect, useRef, useState } from 'react';

interface EnhancedTextareaProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit?: () => void;
  disabled?: boolean;
  className?: string;
  minRows?: number;
  maxRows?: number;
  loading?: boolean;
  showAnalyzeButton?: boolean;
}

export const EnhancedTextarea: React.FC<EnhancedTextareaProps> = ({
  value,
  onChange,
  onSubmit,
  disabled = false,
  className,
  minRows = 4,
  maxRows = 12,
  loading = false,
  showAnalyzeButton = true
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [validation, setValidation] = useState<ValidationResult | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Auto-save functionality
  const { clearDraft, hasDraft, isAutoSaveEnabled } = useAutoSave(value, onChange);

  // Rotating placeholder
  const { placeholder, isVisible } = useRotatingPlaceholder(!isFocused && !value);

  // Character count info
  const charInfo = getCharacterInfo(value);

  // Auto-resize textarea
  const adjustHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    textarea.style.height = 'auto';
    const scrollHeight = textarea.scrollHeight;
    const lineHeight = 24; // Approximate line height
    const minHeight = minRows * lineHeight;
    const maxHeight = maxRows * lineHeight;
    
    const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
    textarea.style.height = `${newHeight}px`;
  }, [minRows, maxRows]);

  // Validate input on change
  useEffect(() => {
    if (value.trim()) {
      const result = validateInput(value);
      setValidation(result);
    } else {
      setValidation(null);
    }
  }, [value]);

  // Adjust height when value changes
  useEffect(() => {
    adjustHeight();
  }, [value, adjustHeight]);

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.ctrlKey && e.key === 'Enter' && onSubmit && !disabled) {
      e.preventDefault();
      onSubmit();
    }
  };

  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
  };

  // Handle blur
  const handleBlur = () => {
    setIsFocused(false);
    setShowSuggestions(false);
  };

  // Toggle suggestions
  const toggleSuggestions = () => {
    setShowSuggestions(!showSuggestions);
  };

  // Get status color
  const getStatusColor = () => {
    if (!validation) return 'border-[var(--border)]';
    if (validation.errors.length > 0) return 'border-[var(--danger)]';
    if (validation.warnings.length > 0) return 'border-yellow-500';
    return 'border-green-500';
  };

  // Get character count color
  const getCharCountColor = () => {
    if (charInfo.status === 'error') return 'text-[var(--danger)]';
    if (charInfo.status === 'warning') return 'text-yellow-500';
    return 'text-[var(--secondary)]';
  };

  return (
    <div className="space-y-3">
      {/* Main textarea container */}
      <div className="relative">
        <div
          className={cn(
            'relative rounded-lg border border-[var(--border)]',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
        >
          <textarea
            ref={textareaRef}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            disabled={disabled}
            className={cn(
              'w-full px-4 py-3 bg-[var(--card)] text-[var(--card-headline)] placeholder-[var(--secondary)] resize-none border-none outline-none rounded-lg',
              'scrollbar-thin scrollbar-thumb-[var(--secondary)] scrollbar-track-transparent',
              showAnalyzeButton && onSubmit ? 'pr-12' : '',
              className
            )}
            placeholder={isVisible ? placeholder : ''}
            style={{
              minHeight: `${minRows * 24}px`,
              maxHeight: `${maxRows * 24}px`,
            }}
          />

          {/* Analyze Button */}
          {showAnalyzeButton && onSubmit && (
            <motion.button
              onClick={onSubmit}
              disabled={disabled || loading || !value.trim() || (validation && validation.errors.length > 0)}
              className={cn(
                'absolute bottom-3 right-3 p-2 rounded-lg transition-all duration-200',
                'bg-[var(--highlight)] text-white hover:bg-[var(--button-hover)]',
                'disabled:opacity-50 disabled:cursor-not-allowed',
                'focus:outline-none focus:ring-2 focus:ring-[var(--highlight)] focus:ring-opacity-20'
              )}
              whileHover={{ scale: disabled || loading ? 1 : 1.05 }}
              whileTap={{ scale: disabled || loading ? 1 : 0.95 }}
              transition={{ duration: 0.1 }}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </motion.button>
          )}

          {/* Auto-save indicator */}
          <AnimatePresence>
            {isAutoSaveEnabled && value && !showAnalyzeButton && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="absolute top-3 right-3 text-[var(--secondary)]"
              >
                <Save className="h-4 w-4" />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Character count and progress */}
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center gap-2">
            {/* Validation status */}
            {validation && (
              <div className="flex items-center gap-1">
                {validation.errors.length > 0 ? (
                  <AlertCircle className="h-4 w-4 text-[var(--danger)]" />
                ) : validation.warnings.length > 0 ? (
                  <Info className="h-4 w-4 text-yellow-500" />
                ) : (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                )}
                <span className="text-xs text-[var(--secondary)]">
                  Score: {validation.score}/100
                </span>
              </div>
            )}

            {/* Suggestions button */}
            {validation && validation.suggestions.length > 0 && (
              <button
                onClick={toggleSuggestions}
                className="flex items-center gap-1 text-xs text-[var(--highlight)] hover:text-[var(--button-hover)] transition-colors"
              >
                <Lightbulb className="h-3 w-3" />
                Suggestions
              </button>
            )}
          </div>

          {/* Character count */}
          <div className="flex items-center gap-2">
            <span className={cn('text-xs', getCharCountColor())}>
              {charInfo.length}/{charInfo.remaining < 0 ? '∞' : charInfo.remaining} remaining
            </span>
            
            {/* Progress bar */}
            <div className="w-16 h-1 bg-[var(--button-secondary)] rounded-full overflow-hidden">
              <motion.div
                className={cn(
                  'h-full transition-colors',
                  charInfo.status === 'error' ? 'bg-[var(--danger)]' :
                  charInfo.status === 'warning' ? 'bg-yellow-500' : 'bg-green-500'
                )}
                initial={{ width: 0 }}
                animate={{ width: `${Math.min(100, charInfo.progress)}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Validation messages */}
      <AnimatePresence>
        {validation && (validation.errors.length > 0 || validation.warnings.length > 0) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-1"
          >
            {validation.errors.map((error, index) => (
              <div key={`error-${index}`} className="flex items-center gap-2 text-xs text-[var(--danger)]">
                <AlertCircle className="h-3 w-3" />
                {error}
              </div>
            ))}
            {validation.warnings.map((warning, index) => (
              <div key={`warning-${index}`} className="flex items-center gap-2 text-xs text-yellow-600">
                <Info className="h-3 w-3" />
                {warning}
              </div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Suggestions */}
      <AnimatePresence>
        {showSuggestions && validation && validation.suggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-[var(--card)] border border-[var(--border)] rounded-lg p-3 space-y-2"
          >
            <div className="flex items-center gap-2 text-sm font-medium text-[var(--card-headline)]">
              <Lightbulb className="h-4 w-4 text-[var(--highlight)]" />
              Suggestions to improve your description:
            </div>
            {validation.suggestions.map((suggestion, index) => (
              <div key={index} className="text-xs text-[var(--card-paragraph)] pl-6">
                • {suggestion}
              </div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Keyboard shortcut hint */}
      {isFocused && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-xs text-[var(--secondary)] text-center"
        >
          Press Ctrl+Enter to analyze
        </motion.div>
      )}
    </div>
  );
};
