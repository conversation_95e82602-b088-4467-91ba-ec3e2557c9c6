import { useState, useEffect, useCallback } from 'react';
import type { SaaSAnalysisResult, SaaSAnalysisInput } from '@/types/saas';
import {
  StoredAnalysis,
  getStoredAnalyses,
  saveAnalysis,
  deleteAnalysis,
  deleteMultipleAnalyses,
  clearAllAnalyses,
  searchAnalyses,
  sortAnalyses,
  getAnalysisById,
  SortCriteria,
  SortOrder,
  getStorageStats
} from '@/utils/cookieStorage';
import { useCookieConsent } from '@/components/common/CookieConsent';

export const useAnalysisStorage = () => {
  const [analyses, setAnalyses] = useState<StoredAnalysis[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortCriteria, setSortCriteria] = useState<SortCriteria>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const { isAccepted } = useCookieConsent();

  // Load analyses from storage
  const loadAnalyses = useCallback(() => {
    if (!isAccepted) {
      setAnalyses([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      let results = getStoredAnalyses();
      
      // Apply search filter
      if (searchQuery.trim()) {
        results = searchAnalyses(searchQuery);
      }
      
      // Apply sorting
      results = sortAnalyses(results, sortCriteria, sortOrder);
      
      setAnalyses(results);
    } catch (error) {
      console.error('Error loading analyses:', error);
      setAnalyses([]);
    } finally {
      setLoading(false);
    }
  }, [isAccepted, searchQuery, sortCriteria, sortOrder]);

  // Load analyses when dependencies change
  useEffect(() => {
    loadAnalyses();
  }, [loadAnalyses]);

  // Save a new analysis
  const save = useCallback(async (
    input: SaaSAnalysisInput,
    result: SaaSAnalysisResult,
    title?: string
  ): Promise<string | null> => {
    if (!isAccepted) {
      return null;
    }

    try {
      const id = saveAnalysis(input, result, title);
      if (id) {
        // Reload analyses to reflect the new addition
        loadAnalyses();
      }
      return id;
    } catch (error) {
      console.error('Error saving analysis:', error);
      return null;
    }
  }, [isAccepted, loadAnalyses]);

  // Delete a single analysis
  const remove = useCallback(async (id: string): Promise<boolean> => {
    if (!isAccepted) {
      return false;
    }

    try {
      const success = deleteAnalysis(id);
      if (success) {
        loadAnalyses();
      }
      return success;
    } catch (error) {
      console.error('Error deleting analysis:', error);
      return false;
    }
  }, [isAccepted, loadAnalyses]);

  // Delete multiple analyses
  const removeMultiple = useCallback(async (ids: string[]): Promise<boolean> => {
    if (!isAccepted || ids.length === 0) {
      return false;
    }

    try {
      const success = deleteMultipleAnalyses(ids);
      if (success) {
        loadAnalyses();
      }
      return success;
    } catch (error) {
      console.error('Error deleting multiple analyses:', error);
      return false;
    }
  }, [isAccepted, loadAnalyses]);

  // Clear all analyses
  const clearAll = useCallback(async (): Promise<boolean> => {
    if (!isAccepted) {
      return false;
    }

    try {
      const success = clearAllAnalyses();
      if (success) {
        loadAnalyses();
      }
      return success;
    } catch (error) {
      console.error('Error clearing all analyses:', error);
      return false;
    }
  }, [isAccepted, loadAnalyses]);

  // Get a specific analysis by ID
  const getById = useCallback((id: string): StoredAnalysis | null => {
    if (!isAccepted) {
      return null;
    }
    return getAnalysisById(id);
  }, [isAccepted]);

  // Update search query
  const updateSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Update sort criteria
  const updateSort = useCallback((criteria: SortCriteria, order: SortOrder = 'desc') => {
    setSortCriteria(criteria);
    setSortOrder(order);
  }, []);

  // Get storage statistics
  const getStats = useCallback(() => {
    return getStorageStats();
  }, []);

  // Refresh analyses (useful after external changes)
  const refresh = useCallback(() => {
    loadAnalyses();
  }, [loadAnalyses]);

  return {
    // Data
    analyses,
    loading,
    searchQuery,
    sortCriteria,
    sortOrder,
    
    // Actions
    save,
    remove,
    removeMultiple,
    clearAll,
    getById,
    updateSearch,
    updateSort,
    refresh,
    getStats,
    
    // State
    isStorageEnabled: isAccepted,
    hasAnalyses: analyses.length > 0
  };
};

export default useAnalysisStorage;
