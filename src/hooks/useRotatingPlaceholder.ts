import { useEffect, useState } from 'react';

const PLACEHOLDER_EXAMPLES = [
  "A project management SaaS for remote teams with time tracking, task automation, and real-time collaboration features targeting small to medium businesses...",
  
  "An AI-powered customer support platform that automates ticket routing, provides intelligent responses, and integrates with existing CRM systems for enterprise clients...",
  
  "A financial planning SaaS for freelancers and small business owners featuring invoice management, expense tracking, tax preparation, and cash flow forecasting...",
  
  "A social media management tool for marketing agencies that schedules posts, analyzes engagement metrics, and manages multiple client accounts from one dashboard...",
  
  "An e-learning platform for corporate training with interactive courses, progress tracking, certification management, and integration with HR systems...",
  
  "A inventory management SaaS for e-commerce businesses with real-time stock tracking, automated reordering, supplier management, and multi-channel integration...",
  
  "A healthcare practice management system with patient scheduling, electronic health records, billing automation, and telemedicine capabilities for small clinics...",
  
  "A real estate CRM platform for agents and brokers featuring lead management, property listings, client communication tools, and market analytics..."
];

const ROTATION_INTERVAL = 4000; // 4 seconds

export const useRotatingPlaceholder = (isActive: boolean = true) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (!isActive) return;

    const interval = setInterval(() => {
      setIsVisible(false);
      
      setTimeout(() => {
        setCurrentIndex((prev) => (prev + 1) % PLACEHOLDER_EXAMPLES.length);
        setIsVisible(true);
      }, 200); // Short fade out duration
      
    }, ROTATION_INTERVAL);

    return () => clearInterval(interval);
  }, [isActive]);

  return {
    placeholder: PLACEHOLDER_EXAMPLES[currentIndex],
    isVisible,
    currentIndex,
    totalExamples: PLACEHOLDER_EXAMPLES.length
  };
};
