import { useState } from "react";
import type { SaaSAnalysisInput, SaaSAnalysisResult } from "../types/saas";
import { analyzeSaaSWithGemini } from "../utils/api/gemini";
import { useAnalysisStorage } from "./useAnalysisStorage";

export function useGemini() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<SaaSAnalysisResult | null>(null);
  const [currentInput, setCurrentInput] = useState<SaaSAnalysisInput | null>(null);
  const { save: saveToStorage } = useAnalysisStorage();

  const analyze = async (input: SaaSAnalysisInput, autoSave: boolean = true) => {
    setLoading(true);
    setError(null);
    setCurrentInput(input);

    try {
      const analysis = await analyzeSaaSWithGemini(input);
      setResult(analysis);

      // Auto-save to storage if cookies are accepted and autoSave is true
      if (autoSave) {
        try {
          await saveToStorage(input, analysis);
        } catch (saveError) {
          console.warn('Failed to save analysis to storage:', saveError);
          // Don't show error to user for storage failures
        }
      }
    } catch (e: any) {
      setError(e.message ?? "Unknown error");
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const saveCurrentResult = async (title?: string) => {
    if (!result || !currentInput) {
      return null;
    }

    try {
      return await saveToStorage(currentInput, result, title);
    } catch (error) {
      console.error('Failed to save current result:', error);
      return null;
    }
  };

  return {
    loading,
    error,
    result,
    analyze,
    setResult,
    saveCurrentResult,
    currentInput
  };
}
