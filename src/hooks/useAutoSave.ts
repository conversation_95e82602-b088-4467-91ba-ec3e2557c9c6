import { useCookieConsent } from '@/components/common/CookieConsent';
import { useCallback, useEffect, useRef } from 'react';

const AUTO_SAVE_KEY = 'saascan_draft';
const AUTO_SAVE_DELAY = 1000; // 1 second delay

export const useAutoSave = (value: string, setValue: (value: string) => void) => {
  const { isAccepted } = useCookieConsent();
  const timeoutRef = useRef<NodeJS.Timeout>();
  const lastSavedRef = useRef<string>('');
  const hasLoadedRef = useRef<boolean>(false);

  // Load draft on mount (only once)
  useEffect(() => {
    if (!isAccepted || hasLoadedRef.current) return;

    try {
      const saved = localStorage.getItem(AUTO_SAVE_KEY);
      if (saved && saved.trim() && !value.trim()) {
        setValue(saved);
        lastSavedRef.current = saved;
      }
      hasLoadedRef.current = true;
    } catch (error) {
      console.warn('Failed to load draft:', error);
    }
  }, [isAccepted, setValue]);

  // Auto-save function
  const saveDraft = useCallback((text: string) => {
    if (!isAccepted || text === lastSavedRef.current) return;

    try {
      if (text.trim()) {
        localStorage.setItem(AUTO_SAVE_KEY, text);
      } else {
        localStorage.removeItem(AUTO_SAVE_KEY);
      }
      lastSavedRef.current = text;
    } catch (error) {
      console.warn('Failed to save draft:', error);
    }
  }, [isAccepted]);

  // Debounced auto-save
  useEffect(() => {
    if (!isAccepted) return;

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      saveDraft(value);
    }, AUTO_SAVE_DELAY);

    // Cleanup
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, saveDraft, isAccepted]);

  // Clear draft function
  const clearDraft = useCallback(() => {
    if (!isAccepted) return;

    try {
      localStorage.removeItem(AUTO_SAVE_KEY);
      lastSavedRef.current = '';
    } catch (error) {
      console.warn('Failed to clear draft:', error);
    }
  }, [isAccepted]);

  // Check if draft exists
  const hasDraft = useCallback(() => {
    if (!isAccepted) return false;

    try {
      const saved = localStorage.getItem(AUTO_SAVE_KEY);
      return Boolean(saved && saved.trim());
    } catch {
      return false;
    }
  }, [isAccepted]);

  return {
    clearDraft,
    hasDraft,
    isAutoSaveEnabled: isAccepted
  };
};
