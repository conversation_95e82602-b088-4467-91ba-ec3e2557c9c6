import { SaasAnalyzer } from "@/components/analysis/SaasAnalyzer";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { <PERSON>rowserRouter } from "react-router-dom";

// Mock the storage functions
jest.mock('@/utils/cookieStorage', () => ({
  getStoredAnalyses: jest.fn(() => []),
  saveAnalysis: jest.fn(() => 'test-id'),
  deleteAnalysis: jest.fn(() => true),
  deleteMultipleAnalyses: jest.fn(() => true),
  clearAllAnalyses: jest.fn(() => true),
  searchAnalyses: jest.fn(() => []),
  sortAnalyses: jest.fn((analyses) => analyses),
  getAnalysisById: jest.fn(() => null),
  getStorageStats: jest.fn(() => ({ totalAnalyses: 0, storageUsed: 0, lastAnalysis: null }))
}));

// Mock the cookie consent
jest.mock('@/components/common/CookieConsent', () => ({
  useCookieConsent: () => ({
    isAccepted: true,
    isDeclined: false,
    hasChoiceMade: true,
    consent: 'accepted'
  })
}));

// Mock the Gemini API
jest.mock('@/utils/api/gemini', () => ({
  analyzeSaaSWithGemini: jest.fn(() => Promise.resolve({
    executiveSummary: "Test summary",
    keyMetrics: {
      churnRate: 5,
      mrr: 10000,
      arr: 120000,
      clv: 2000,
      engagement: 75,
      retention: 85
    },
    personaRecommendations: "Test personas",
    onboardingSuggestions: "Test onboarding",
    integrationOpportunities: "Test integrations",
    auditingSecurityCompliance: "Test security",
    multiTenancyAssessment: "Test multi-tenancy",
    customerJourney: ["Step 1", "Step 2"],
    actionableRecommendations: "Test recommendations"
  }))
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </BrowserRouter>
  );
};

test("renders input and runs analysis", async () => {
  render(
    <TestWrapper>
      <SaasAnalyzer />
    </TestWrapper>
  );

  const input = screen.getByLabelText(/SaaS Description/i);
  fireEvent.change(input, { target: { value: "A SaaS for B2B invoicing." } });

  const analyzeButton = screen.getByRole("button", { name: /analyze/i });
  fireEvent.click(analyzeButton);

  expect(analyzeButton).toBeDisabled();

  // Wait for the analysis to complete
  await waitFor(() => {
    expect(screen.getByText("Test summary")).toBeInTheDocument();
  }, { timeout: 3000 });
});

test("renders clear button and clears input", () => {
  render(
    <TestWrapper>
      <SaasAnalyzer />
    </TestWrapper>
  );

  const input = screen.getByLabelText(/SaaS Description/i);
  fireEvent.change(input, { target: { value: "Test input" } });

  const clearButton = screen.getByRole("button", { name: /clear/i });
  fireEvent.click(clearButton);

  expect(input).toHaveValue("");
});
