import { analyzeSaaSWithGemini } from '../utils/api/gemini';
import { getGeminiApiKey, saveGeminiApiKey } from '../utils/apiKeyStorage';

// Mock the API key storage
jest.mock('../utils/apiKeyStorage', () => ({
  getGeminiApiKey: jest.fn(),
  saveGeminiApiKey: jest.fn(),
  removeGeminiApiKey: jest.fn(),
  validateApiKey: jest.fn(),
  hasGeminiApiKey: jest.fn(),
}));

// Mock the Google Generative AI
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
    getGenerativeModel: jest.fn().mockReturnValue({
      generateContent: jest.fn().mockResolvedValue({
        response: {
          text: () => JSON.stringify({
            executiveSummary: "Test SaaS analysis summary",
            overallScore: 85,
            recommendation: "go",
            problemSolution: {
              problemStatement: "Test problem statement",
              problemSignificance: 8,
              targetAudiencePainPoints: ["Pain point 1", "Pain point 2"],
              proposedSolution: "Test solution",
              uniqueValueProposition: "Test value proposition",
              existingAlternatives: ["Alternative 1"],
              competitiveAdvantages: ["Advantage 1"]
            },
            marketAnalysis: {
              idealCustomerSegments: [{
                name: "Test Segment",
                demographics: "Test demographics",
                psychographics: "Test psychographics",
                behaviors: ["Behavior 1"],
                painPoints: ["Pain 1"],
                willingness_to_pay: 100
              }],
              totalAddressableMarket: {
                value: 1000000000,
                currency: "USD",
                growthRate: 15
              },
              serviceableAddressableMarket: {
                value: 100000000,
                currency: "USD",
                penetrationRate: 2
              },
              marketResearchInsights: ["Insight 1"],
              customerValidationData: ["Validation 1"]
            },
            businessModel: {
              keyMetrics: {
                mrr: 50000,
                arr: 600000,
                customers: 500,
                cac: 150,
                ltv: 2400,
                churnRate: 5,
                grossMargin: 80,
                burnRate: 100000,
                engagement: 75,
                retention: 85
              }
            }
          })
        }
      })
    })
  })),
  HarmCategory: {
    HARM_CATEGORY_HARASSMENT: 'HARM_CATEGORY_HARASSMENT',
    HARM_CATEGORY_HATE_SPEECH: 'HARM_CATEGORY_HATE_SPEECH',
    HARM_CATEGORY_SEXUALLY_EXPLICIT: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
    HARM_CATEGORY_DANGEROUS_CONTENT: 'HARM_CATEGORY_DANGEROUS_CONTENT',
  },
  HarmBlockThreshold: {
    BLOCK_MEDIUM_AND_ABOVE: 'BLOCK_MEDIUM_AND_ABOVE',
  },
}));

describe('Gemini Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (getGeminiApiKey as jest.Mock).mockReturnValue('AIzaSyCU6wEazb7dvTZnVV9BtaFk39sg52d4-IQ');
  });

  it('should analyze SaaS input successfully', async () => {
    const input = {
      description: 'A project management tool for remote teams'
    };

    const result = await analyzeSaaSWithGemini(input);

    expect(result).toBeDefined();
    expect(result.executiveSummary).toBe('Test SaaS analysis summary');
    expect(result.overallScore).toBe(85);
    expect(result.recommendation).toBe('go');
    expect(result.keyMetrics).toBeDefined();
    expect(result.keyMetrics.mrr).toBe(50000);
  });

  it('should throw error when API key is missing', async () => {
    (getGeminiApiKey as jest.Mock).mockReturnValue(null);

    const input = {
      description: 'A project management tool for remote teams'
    };

    await expect(analyzeSaaSWithGemini(input)).rejects.toThrow(
      'Gemini API key not found. Please set VITE_GEMINI_API_KEY in environment or save key in settings.'
    );
  });

  it('should handle API errors gracefully', async () => {
    const mockError = new Error('API quota exceeded');
    mockError.message = 'quota exceeded';

    // Mock the Google Generative AI to throw an error
    jest.doMock('@google/generative-ai', () => ({
      GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
        getGenerativeModel: jest.fn().mockReturnValue({
          generateContent: jest.fn().mockRejectedValue(mockError)
        })
      })),
      HarmCategory: {
        HARM_CATEGORY_HARASSMENT: 'HARM_CATEGORY_HARASSMENT',
        HARM_CATEGORY_HATE_SPEECH: 'HARM_CATEGORY_HATE_SPEECH',
        HARM_CATEGORY_SEXUALLY_EXPLICIT: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
        HARM_CATEGORY_DANGEROUS_CONTENT: 'HARM_CATEGORY_DANGEROUS_CONTENT',
      },
      HarmBlockThreshold: {
        BLOCK_MEDIUM_AND_ABOVE: 'BLOCK_MEDIUM_AND_ABOVE',
      },
    }));

    const input = {
      description: 'A project management tool for remote teams'
    };

    await expect(analyzeSaaSWithGemini(input)).rejects.toThrow(
      'Gemini API quota exceeded. Please try again later or check your billing.'
    );
  });
});
