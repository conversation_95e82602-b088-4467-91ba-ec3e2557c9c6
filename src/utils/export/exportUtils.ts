import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';
import html2canvas from 'html2canvas';
import type { SaaSAnalysisResult } from '@/types/saas';
import { format } from 'date-fns';

export const exportToPDF = async (result: SaaSAnalysisResult, elementId?: string): Promise<void> => {
  try {
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    const margin = 20;
    let yPosition = margin;

    // Helper function to add text with word wrapping
    const addText = (text: string, fontSize: number = 12, isBold: boolean = false) => {
      pdf.setFontSize(fontSize);
      pdf.setFont('helvetica', isBold ? 'bold' : 'normal');
      
      const lines = pdf.splitTextToSize(text, pageWidth - 2 * margin);
      
      // Check if we need a new page
      if (yPosition + lines.length * fontSize * 0.35 > pageHeight - margin) {
        pdf.addPage();
        yPosition = margin;
      }
      
      pdf.text(lines, margin, yPosition);
      yPosition += lines.length * fontSize * 0.35 + 5;
    };

    const addSection = (title: string, content: string) => {
      addText(title, 16, true);
      addText(content, 12, false);
      yPosition += 5;
    };

    // Title
    addText('SaaS Analysis Report', 24, true);
    addText(`Generated on ${format(new Date(), 'PPP')}`, 10, false);
    yPosition += 10;

    // Executive Summary
    addSection('Executive Summary', result.executiveSummary);
    
    // Overall Score and Recommendation
    addText(`Overall Score: ${result.overallScore}/100`, 14, true);
    addText(`Recommendation: ${result.recommendation.toUpperCase()}`, 14, true);
    yPosition += 10;

    // Problem & Solution Analysis
    addSection('Problem Statement', result.problemSolution.problemStatement);
    addSection('Proposed Solution', result.problemSolution.proposedSolution);
    addSection('Unique Value Proposition', result.problemSolution.uniqueValueProposition);

    // Market Analysis
    addSection('Total Addressable Market', 
      `$${(result.marketAnalysis.totalAddressableMarket.value / 1000000000).toFixed(1)}B (${result.marketAnalysis.totalAddressableMarket.growthRate}% growth)`
    );
    
    addSection('Serviceable Addressable Market', 
      `$${(result.marketAnalysis.serviceableAddressableMarket.value / 1000000000).toFixed(1)}B (${result.marketAnalysis.serviceableAddressableMarket.penetrationRate}% penetration)`
    );

    // Financial Metrics
    addSection('Key Financial Metrics', 
      `MRR: $${result.businessModel.keyMetrics.mrr.toLocaleString()}\n` +
      `ARR: $${result.businessModel.keyMetrics.arr.toLocaleString()}\n` +
      `Customers: ${result.businessModel.keyMetrics.customers.toLocaleString()}\n` +
      `CAC: $${result.businessModel.keyMetrics.cac}\n` +
      `LTV: $${result.businessModel.keyMetrics.ltv}\n` +
      `Churn Rate: ${result.businessModel.keyMetrics.churnRate}%\n` +
      `Gross Margin: ${result.businessModel.keyMetrics.grossMargin}%`
    );

    // Competitive Landscape
    const competitorText = result.competitiveLandscape.competitors
      .map(comp => `${comp.name} (${comp.type}, ${comp.marketShare}% market share)`)
      .join('\n');
    addSection('Key Competitors', competitorText);

    // Risk Assessment
    addSection('Overall Risk Score', `${result.riskAssessment.overallRiskScore}/10`);
    
    const riskText = result.riskAssessment.riskFactors
      .map(risk => `${risk.category}: ${risk.description} (Probability: ${risk.probability}, Impact: ${risk.impact})`)
      .join('\n');
    addSection('Key Risk Factors', riskText);

    // Strategic Recommendations
    addSection('Strategic Recommendation', result.strategicRecommendations.recommendation.toUpperCase());
    addSection('Confidence Level', `${result.strategicRecommendations.confidence}/10`);
    
    const rationaleText = result.strategicRecommendations.rationale.join('\n• ');
    addSection('Rationale', '• ' + rationaleText);

    const nextStepsText = result.strategicRecommendations.nextSteps.join('\n• ');
    addSection('Next Steps', '• ' + nextStepsText);

    // If an element ID is provided, capture it as an image
    if (elementId) {
      const element = document.getElementById(elementId);
      if (element) {
        try {
          const canvas = await html2canvas(element, {
            scale: 2,
            useCORS: true,
            allowTaint: true
          });
          
          const imgData = canvas.toDataURL('image/png');
          const imgWidth = pageWidth - 2 * margin;
          const imgHeight = (canvas.height * imgWidth) / canvas.width;
          
          // Add new page for charts
          pdf.addPage();
          pdf.addImage(imgData, 'PNG', margin, margin, imgWidth, imgHeight);
        } catch (error) {
          console.warn('Could not capture charts for PDF:', error);
        }
      }
    }

    // Save the PDF
    pdf.save(`saas-analysis-${format(new Date(), 'yyyy-MM-dd')}.pdf`);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF report');
  }
};

export const exportToExcel = (result: SaaSAnalysisResult): void => {
  try {
    const workbook = XLSX.utils.book_new();

    // Executive Summary Sheet
    const summaryData = [
      ['SaaS Analysis Report'],
      ['Generated on', format(new Date(), 'PPP')],
      [''],
      ['Overall Score', result.overallScore],
      ['Recommendation', result.recommendation.toUpperCase()],
      [''],
      ['Executive Summary', result.executiveSummary],
    ];
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Executive Summary');

    // Problem & Solution Sheet
    const problemSolutionData = [
      ['Problem & Solution Analysis'],
      [''],
      ['Problem Statement', result.problemSolution.problemStatement],
      ['Problem Significance (1-10)', result.problemSolution.problemSignificance],
      ['Proposed Solution', result.problemSolution.proposedSolution],
      ['Unique Value Proposition', result.problemSolution.uniqueValueProposition],
      [''],
      ['Target Audience Pain Points'],
      ...result.problemSolution.targetAudiencePainPoints.map(point => ['', point]),
      [''],
      ['Competitive Advantages'],
      ...result.problemSolution.competitiveAdvantages.map(advantage => ['', advantage]),
    ];
    const problemSolutionSheet = XLSX.utils.aoa_to_sheet(problemSolutionData);
    XLSX.utils.book_append_sheet(workbook, problemSolutionSheet, 'Problem & Solution');

    // Market Analysis Sheet
    const marketData = [
      ['Market Analysis'],
      [''],
      ['Total Addressable Market', `$${result.marketAnalysis.totalAddressableMarket.value.toLocaleString()}`],
      ['TAM Growth Rate', `${result.marketAnalysis.totalAddressableMarket.growthRate}%`],
      ['Serviceable Addressable Market', `$${result.marketAnalysis.serviceableAddressableMarket.value.toLocaleString()}`],
      ['SAM Penetration Rate', `${result.marketAnalysis.serviceableAddressableMarket.penetrationRate}%`],
      [''],
      ['Customer Segments'],
      ['Name', 'Demographics', 'Willingness to Pay'],
      ...result.marketAnalysis.idealCustomerSegments.map(segment => [
        segment.name,
        segment.demographics,
        `$${segment.willingness_to_pay}`
      ]),
    ];
    const marketSheet = XLSX.utils.aoa_to_sheet(marketData);
    XLSX.utils.book_append_sheet(workbook, marketSheet, 'Market Analysis');

    // Financial Projections Sheet
    const financialData = [
      ['Financial Projections'],
      [''],
      ['Metric', 'Year 1', 'Year 2', 'Year 3', 'Year 5'],
      ['MRR', result.businessModel.financialProjections.year1.mrr, result.businessModel.financialProjections.year2.mrr, result.businessModel.financialProjections.year3.mrr, result.businessModel.financialProjections.year5.mrr],
      ['ARR', result.businessModel.financialProjections.year1.arr, result.businessModel.financialProjections.year2.arr, result.businessModel.financialProjections.year3.arr, result.businessModel.financialProjections.year5.arr],
      ['Customers', result.businessModel.financialProjections.year1.customers, result.businessModel.financialProjections.year2.customers, result.businessModel.financialProjections.year3.customers, result.businessModel.financialProjections.year5.customers],
      ['CAC', result.businessModel.financialProjections.year1.cac, result.businessModel.financialProjections.year2.cac, result.businessModel.financialProjections.year3.cac, result.businessModel.financialProjections.year5.cac],
      ['LTV', result.businessModel.financialProjections.year1.ltv, result.businessModel.financialProjections.year2.ltv, result.businessModel.financialProjections.year3.ltv, result.businessModel.financialProjections.year5.ltv],
      ['Churn Rate (%)', result.businessModel.financialProjections.year1.churnRate, result.businessModel.financialProjections.year2.churnRate, result.businessModel.financialProjections.year3.churnRate, result.businessModel.financialProjections.year5.churnRate],
      ['Gross Margin (%)', result.businessModel.financialProjections.year1.grossMargin, result.businessModel.financialProjections.year2.grossMargin, result.businessModel.financialProjections.year3.grossMargin, result.businessModel.financialProjections.year5.grossMargin],
    ];
    const financialSheet = XLSX.utils.aoa_to_sheet(financialData);
    XLSX.utils.book_append_sheet(workbook, financialSheet, 'Financial Projections');

    // Competitive Analysis Sheet
    const competitiveData = [
      ['Competitive Analysis'],
      [''],
      ['Competitor', 'Type', 'Market Share (%)', 'Pricing Strategy'],
      ...result.competitiveLandscape.competitors.map(comp => [
        comp.name,
        comp.type,
        comp.marketShare,
        comp.pricingStrategy
      ]),
      [''],
      ['Market Positioning', result.competitiveLandscape.marketPositioning],
    ];
    const competitiveSheet = XLSX.utils.aoa_to_sheet(competitiveData);
    XLSX.utils.book_append_sheet(workbook, competitiveSheet, 'Competitive Analysis');

    // Risk Assessment Sheet
    const riskData = [
      ['Risk Assessment'],
      [''],
      ['Overall Risk Score (1-10)', result.riskAssessment.overallRiskScore],
      [''],
      ['Risk Factors'],
      ['Category', 'Description', 'Probability (1-10)', 'Impact (1-10)', 'Mitigation'],
      ...result.riskAssessment.riskFactors.map(risk => [
        risk.category,
        risk.description,
        risk.probability,
        risk.impact,
        risk.mitigation
      ]),
      [''],
      ['SWOT Analysis'],
      ['Strengths'],
      ...result.riskAssessment.swotAnalysis.strengths.map(item => ['', item]),
      ['Weaknesses'],
      ...result.riskAssessment.swotAnalysis.weaknesses.map(item => ['', item]),
      ['Opportunities'],
      ...result.riskAssessment.swotAnalysis.opportunities.map(item => ['', item]),
      ['Threats'],
      ...result.riskAssessment.swotAnalysis.threats.map(item => ['', item]),
    ];
    const riskSheet = XLSX.utils.aoa_to_sheet(riskData);
    XLSX.utils.book_append_sheet(workbook, riskSheet, 'Risk Assessment');

    // Strategic Recommendations Sheet
    const strategyData = [
      ['Strategic Recommendations'],
      [''],
      ['Recommendation', result.strategicRecommendations.recommendation.toUpperCase()],
      ['Confidence (1-10)', result.strategicRecommendations.confidence],
      [''],
      ['Rationale'],
      ...result.strategicRecommendations.rationale.map(reason => ['', reason]),
      [''],
      ['Next Steps'],
      ...result.strategicRecommendations.nextSteps.map(step => ['', step]),
      [''],
      ['MVP Priorities'],
      ...result.strategicRecommendations.mvpPriorities.map(priority => ['', priority]),
    ];
    const strategySheet = XLSX.utils.aoa_to_sheet(strategyData);
    XLSX.utils.book_append_sheet(workbook, strategySheet, 'Strategic Recommendations');

    // Save the Excel file
    XLSX.writeFile(workbook, `saas-analysis-${format(new Date(), 'yyyy-MM-dd')}.xlsx`);
  } catch (error) {
    console.error('Error generating Excel file:', error);
    throw new Error('Failed to generate Excel report');
  }
};
