import type { SaaSAnalysisInput, SaaSAnalysisResult } from '../../types/saas';

// Note: The key must be set in `.env` as REACT_APP_GEMINI_API_KEY
const API_KEY ='AIzaSyCU6wEazb7dvTZnVV9BtaFk39sg52d4-IQ';

export const analyzeSaaSWithGemini = async (
  input: SaaSAnalysisInput
): Promise<SaaSAnalysisResult> => {
  try {
    const resp = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=' + API_KEY, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: `You are a senior SaaS business analyst and venture capital expert. Analyze the following SaaS description and provide comprehensive business intelligence. Return ONLY a valid JSON object with the exact structure below. Do not include any explanatory text before or after the JSON.

SaaS Description: ${input.description}

Provide a thorough analysis covering all aspects of the business opportunity. Return this exact JSON structure with realistic, data-driven insights:

{
  "executiveSummary": "Comprehensive 3-4 sentence executive summary",
  "overallScore": 75,
  "recommendation": "go",
  "problemSolution": {
    "problemStatement": "Clear problem definition",
    "problemSignificance": 8,
    "targetAudiencePainPoints": ["Pain point 1", "Pain point 2", "Pain point 3"],
    "proposedSolution": "Solution description",
    "uniqueValueProposition": "Unique value proposition",
    "existingAlternatives": ["Alternative 1", "Alternative 2"],
    "competitiveAdvantages": ["Advantage 1", "Advantage 2"]
  },
  "marketAnalysis": {
    "idealCustomerSegments": [
      {
        "name": "Primary Segment",
        "demographics": "Demographics description",
        "psychographics": "Psychographics description",
        "behaviors": ["Behavior 1", "Behavior 2"],
        "painPoints": ["Pain 1", "Pain 2"],
        "willingness_to_pay": 100
      }
    ],
    "totalAddressableMarket": {
      "value": 50000000000,
      "currency": "USD",
      "growthRate": 15
    },
    "serviceableAddressableMarket": {
      "value": 5000000000,
      "currency": "USD",
      "penetrationRate": 2
    },
    "marketResearchInsights": ["Insight 1", "Insight 2"],
    "customerValidationData": ["Validation 1", "Validation 2"]
  },
  "competitiveLandscape": {
    "competitors": [
      {
        "name": "Competitor 1",
        "type": "direct",
        "marketShare": 25,
        "strengths": ["Strength 1", "Strength 2"],
        "weaknesses": ["Weakness 1", "Weakness 2"],
        "pricingStrategy": "Pricing strategy",
        "keyFeatures": ["Feature 1", "Feature 2"]
      }
    ],
    "marketPositioning": "Market positioning strategy",
    "competitiveAdvantages": ["Advantage 1", "Advantage 2"],
    "threats": ["Threat 1", "Threat 2"],
    "opportunities": ["Opportunity 1", "Opportunity 2"]
  },
  "marketValidation": {
    "validationMethodologies": ["Method 1", "Method 2"],
    "quantitativeEvidence": [
      {
        "methodology": "Landing page test",
        "metric": "Conversion rate",
        "value": 3.5,
        "significance": "Strong interest"
      }
    ],
    "conversionRates": {
      "landingPage": 3.5,
      "signUp": 15,
      "trial": 25,
      "paid": 8
    },
    "demandSignals": ["Signal 1", "Signal 2"],
    "userFeedbackScores": {
      "problemFit": 8,
      "solutionFit": 7,
      "usability": 6,
      "value": 8
    }
  },
  "businessModel": {
    "pricingStrategy": "Freemium with tiered subscriptions",
    "pricingTiers": [
      {
        "name": "Basic",
        "price": 29,
        "features": ["Feature 1", "Feature 2"],
        "targetSegment": "Small businesses"
      }
    ],
    "revenueStreams": ["Subscriptions", "Premium features"],
    "financialProjections": {
      "year1": {
        "mrr": 50000,
        "arr": 600000,
        "customers": 500,
        "cac": 150,
        "ltv": 2400,
        "churnRate": 5,
        "grossMargin": 80,
        "burnRate": 100000,
        "engagement": 75,
        "retention": 85
      },
      "year2": {
        "mrr": 200000,
        "arr": 2400000,
        "customers": 2000,
        "cac": 120,
        "ltv": 3000,
        "churnRate": 4,
        "grossMargin": 82,
        "burnRate": 150000,
        "engagement": 78,
        "retention": 87
      },
      "year3": {
        "mrr": 500000,
        "arr": 6000000,
        "customers": 5000,
        "cac": 100,
        "ltv": 3600,
        "churnRate": 3,
        "grossMargin": 85,
        "burnRate": 200000,
        "engagement": 80,
        "retention": 90
      },
      "year5": {
        "mrr": 1500000,
        "arr": 18000000,
        "customers": 15000,
        "cac": 80,
        "ltv": 4800,
        "churnRate": 2,
        "grossMargin": 88,
        "burnRate": 300000,
        "engagement": 85,
        "retention": 92
      }
    },
    "keyMetrics": {
      "mrr": 50000,
      "arr": 600000,
      "customers": 500,
      "cac": 150,
      "ltv": 2400,
      "churnRate": 5,
      "grossMargin": 80,
      "burnRate": 100000,
      "engagement": 75,
      "retention": 85
    },
    "industryBenchmarks": {
      "churnRate": {"value": 5, "percentile": 60},
      "cac": {"value": 150, "percentile": 70},
      "ltv": {"value": 2400, "percentile": 65},
      "grossMargin": {"value": 80, "percentile": 75}
    }
  },
  "riskAssessment": {
    "riskFactors": [
      {
        "category": "market",
        "description": "Market adoption risk",
        "probability": 6,
        "impact": 8,
        "mitigation": "Mitigation strategy"
      }
    ],
    "founderMarketFit": {
      "domainExpertise": 7,
      "technicalCapability": 8,
      "businessExperience": 6,
      "resourceAvailability": 7,
      "networkStrength": 6,
      "overallScore": 6.8
    },
    "swotAnalysis": {
      "strengths": ["Strength 1", "Strength 2"],
      "weaknesses": ["Weakness 1", "Weakness 2"],
      "opportunities": ["Opportunity 1", "Opportunity 2"],
      "threats": ["Threat 1", "Threat 2"]
    },
    "overallRiskScore": 6
  },
  "strategicRecommendations": {
    "recommendation": "go",
    "confidence": 8,
    "rationale": ["Reason 1", "Reason 2"],
    "validationExperiments": [
      {
        "name": "Landing page test",
        "description": "Test description",
        "timeline": "2 weeks",
        "budget": 5000,
        "successMetrics": ["Metric 1", "Metric 2"],
        "priority": "high"
      }
    ],
    "mvpPriorities": ["Priority 1", "Priority 2"],
    "successMetrics": ["Metric 1", "Metric 2"],
    "milestones": [
      {
        "name": "MVP Launch",
        "description": "Launch description",
        "timeline": "3 months",
        "dependencies": ["Dependency 1"],
        "successCriteria": ["Criteria 1"]
      }
    ],
    "nextSteps": ["Step 1", "Step 2"]
  },
  "securityCompliance": {
    "requirements": ["GDPR", "SOC 2"],
    "frameworks": ["ISO 27001"],
    "certifications": ["SOC 2 Type II"],
    "implementationCost": 50000,
    "timeline": "6 months"
  },
  "technicalArchitecture": {
    "scalabilityAssessment": "High scalability potential",
    "technologyStack": ["React", "Node.js", "PostgreSQL"],
    "infrastructureRequirements": ["Cloud hosting", "CDN"],
    "developmentComplexity": 7,
    "maintenanceCost": 20000
  },
  "marketTiming": {
    "marketMaturity": "Growing",
    "trendAnalysis": ["Trend 1", "Trend 2"],
    "timingScore": 8,
    "marketReadiness": "Ready"
  },
  "personaRecommendations": "Target persona recommendations",
  "onboardingSuggestions": "Onboarding and UX suggestions",
  "integrationOpportunities": "Integration opportunities",
  "auditingSecurityCompliance": "Security and compliance recommendations",
  "multiTenancyAssessment": "Multi-tenancy assessment",
  "customerJourney": ["Discovery", "Trial", "Conversion", "Retention"],
  "actionableRecommendations": "Actionable recommendations"
}

Ensure all numerical values are realistic and based on industry standards. Provide specific, actionable insights rather than generic statements.`
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 8192,
        }
      })
    });

    if (!resp.ok) {
      const errorText = await resp.text();
      throw new Error(`Gemini API error: ${resp.status} - ${errorText}`);
    }

    const data = await resp.json();

    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
      throw new Error('Invalid response structure from Gemini API');
    }

    const responseText = data.candidates[0].content.parts[0].text;

    // Clean the response text to extract JSON
    let cleanedText = responseText.trim();

    // Remove any markdown code blocks
    cleanedText = cleanedText.replace(/```json\s*/g, '').replace(/```\s*/g, '');

    // Find JSON object boundaries
    const jsonStart = cleanedText.indexOf('{');
    const jsonEnd = cleanedText.lastIndexOf('}') + 1;

    if (jsonStart === -1 || jsonEnd === 0) {
      throw new Error('No valid JSON found in Gemini response');
    }

    const jsonString = cleanedText.substring(jsonStart, jsonEnd);

    let json: any;
    try {
      json = JSON.parse(jsonString);
    } catch (parseError) {
      console.error('Failed to parse JSON:', jsonString);
      throw new Error('Invalid JSON format in Gemini response');
    }

    // Map Gemini response to our comprehensive SaaSAnalysisResult
    return {
      executiveSummary: json.executiveSummary || 'Analysis completed successfully.',
      overallScore: Number(json.overallScore) || 75,
      recommendation: json.recommendation || 'go',

      // Core Analysis Sections
      problemSolution: json.problemSolution || {
        problemStatement: 'Problem analysis needed',
        problemSignificance: 7,
        targetAudiencePainPoints: ['Pain point analysis required'],
        proposedSolution: 'Solution analysis needed',
        uniqueValueProposition: 'Value proposition analysis needed',
        existingAlternatives: ['Alternative analysis required'],
        competitiveAdvantages: ['Advantage analysis required']
      },

      marketAnalysis: json.marketAnalysis || {
        idealCustomerSegments: [{
          name: 'Primary Segment',
          demographics: 'Demographics analysis needed',
          psychographics: 'Psychographics analysis needed',
          behaviors: ['Behavior analysis required'],
          painPoints: ['Pain point analysis required'],
          willingness_to_pay: 100
        }],
        totalAddressableMarket: {
          value: 1000000000,
          currency: 'USD',
          growthRate: 10
        },
        serviceableAddressableMarket: {
          value: 100000000,
          currency: 'USD',
          penetrationRate: 1
        },
        marketResearchInsights: ['Market research needed'],
        customerValidationData: ['Validation data needed']
      },

      competitiveLandscape: json.competitiveLandscape || {
        competitors: [{
          name: 'Competitor Analysis Needed',
          type: 'direct' as const,
          marketShare: 20,
          strengths: ['Strength analysis required'],
          weaknesses: ['Weakness analysis required'],
          pricingStrategy: 'Pricing analysis needed',
          keyFeatures: ['Feature analysis required']
        }],
        marketPositioning: 'Positioning analysis needed',
        competitiveAdvantages: ['Advantage analysis required'],
        threats: ['Threat analysis required'],
        opportunities: ['Opportunity analysis required']
      },

      marketValidation: json.marketValidation || {
        validationMethodologies: ['Validation methods needed'],
        quantitativeEvidence: [{
          methodology: 'Method analysis needed',
          metric: 'Metric analysis needed',
          value: 0,
          significance: 'Significance analysis needed'
        }],
        conversionRates: {
          landingPage: 2,
          signUp: 10,
          trial: 20,
          paid: 5
        },
        demandSignals: ['Demand signal analysis needed'],
        userFeedbackScores: {
          problemFit: 7,
          solutionFit: 6,
          usability: 6,
          value: 7
        }
      },

      businessModel: json.businessModel || {
        pricingStrategy: 'Pricing strategy analysis needed',
        pricingTiers: [{
          name: 'Basic',
          price: 29,
          features: ['Feature analysis needed'],
          targetSegment: 'Segment analysis needed'
        }],
        revenueStreams: ['Revenue stream analysis needed'],
        financialProjections: {
          year1: {
            mrr: 50000,
            arr: 600000,
            customers: 500,
            cac: 150,
            ltv: 2400,
            churnRate: 5,
            grossMargin: 80,
            burnRate: 100000,
            engagement: 75,
            retention: 85
          },
          year2: {
            mrr: 200000,
            arr: 2400000,
            customers: 2000,
            cac: 120,
            ltv: 3000,
            churnRate: 4,
            grossMargin: 82,
            burnRate: 150000,
            engagement: 78,
            retention: 87
          },
          year3: {
            mrr: 500000,
            arr: 6000000,
            customers: 5000,
            cac: 100,
            ltv: 3600,
            churnRate: 3,
            grossMargin: 85,
            burnRate: 200000,
            engagement: 80,
            retention: 90
          },
          year5: {
            mrr: 1500000,
            arr: 18000000,
            customers: 15000,
            cac: 80,
            ltv: 4800,
            churnRate: 2,
            grossMargin: 88,
            burnRate: 300000,
            engagement: 85,
            retention: 92
          }
        },
        keyMetrics: {
          mrr: Number(json.businessModel?.keyMetrics?.mrr) || 50000,
          arr: Number(json.businessModel?.keyMetrics?.arr) || 600000,
          customers: Number(json.businessModel?.keyMetrics?.customers) || 500,
          cac: Number(json.businessModel?.keyMetrics?.cac) || 150,
          ltv: Number(json.businessModel?.keyMetrics?.ltv) || 2400,
          churnRate: Number(json.businessModel?.keyMetrics?.churnRate) || 5,
          grossMargin: Number(json.businessModel?.keyMetrics?.grossMargin) || 80,
          burnRate: Number(json.businessModel?.keyMetrics?.burnRate) || 100000,
          engagement: Number(json.businessModel?.keyMetrics?.engagement) || 75,
          retention: Number(json.businessModel?.keyMetrics?.retention) || 85
        },
        industryBenchmarks: {
          churnRate: { value: 5, percentile: 60 },
          cac: { value: 150, percentile: 70 },
          ltv: { value: 2400, percentile: 65 },
          grossMargin: { value: 80, percentile: 75 }
        }
      },

      riskAssessment: json.riskAssessment || {
        riskFactors: [{
          category: 'market' as const,
          description: 'Risk analysis needed',
          probability: 5,
          impact: 5,
          mitigation: 'Mitigation strategy needed'
        }],
        founderMarketFit: {
          domainExpertise: 7,
          technicalCapability: 7,
          businessExperience: 6,
          resourceAvailability: 7,
          networkStrength: 6,
          overallScore: 6.6
        },
        swotAnalysis: {
          strengths: ['SWOT analysis needed'],
          weaknesses: ['SWOT analysis needed'],
          opportunities: ['SWOT analysis needed'],
          threats: ['SWOT analysis needed']
        },
        overallRiskScore: 6
      },

      strategicRecommendations: json.strategicRecommendations || {
        recommendation: 'go' as const,
        confidence: 7,
        rationale: ['Strategic analysis needed'],
        validationExperiments: [{
          name: 'Validation experiment needed',
          description: 'Experiment description needed',
          timeline: '2 weeks',
          budget: 5000,
          successMetrics: ['Success metrics needed'],
          priority: 'medium' as const
        }],
        mvpPriorities: ['MVP priorities needed'],
        successMetrics: ['Success metrics needed'],
        milestones: [{
          name: 'Milestone analysis needed',
          description: 'Milestone description needed',
          timeline: '3 months',
          dependencies: ['Dependencies needed'],
          successCriteria: ['Success criteria needed']
        }],
        nextSteps: ['Next steps needed']
      },

      securityCompliance: json.securityCompliance || {
        requirements: ['Security requirements analysis needed'],
        frameworks: ['Framework analysis needed'],
        certifications: ['Certification analysis needed'],
        implementationCost: 50000,
        timeline: '6 months'
      },

      technicalArchitecture: json.technicalArchitecture || {
        scalabilityAssessment: 'Scalability analysis needed',
        technologyStack: ['Technology stack analysis needed'],
        infrastructureRequirements: ['Infrastructure analysis needed'],
        developmentComplexity: 7,
        maintenanceCost: 20000
      },

      marketTiming: json.marketTiming || {
        marketMaturity: 'Market timing analysis needed',
        trendAnalysis: ['Trend analysis needed'],
        timingScore: 7,
        marketReadiness: 'Market readiness analysis needed'
      },

      // Legacy fields for backward compatibility
      keyMetrics: {
        mrr: Number(json.businessModel?.keyMetrics?.mrr) || 50000,
        arr: Number(json.businessModel?.keyMetrics?.arr) || 600000,
        customers: Number(json.businessModel?.keyMetrics?.customers) || 500,
        cac: Number(json.businessModel?.keyMetrics?.cac) || 150,
        ltv: Number(json.businessModel?.keyMetrics?.ltv) || 2400,
        churnRate: Number(json.businessModel?.keyMetrics?.churnRate) || 5,
        grossMargin: Number(json.businessModel?.keyMetrics?.grossMargin) || 80,
        burnRate: Number(json.businessModel?.keyMetrics?.burnRate) || 100000,
        engagement: Number(json.businessModel?.keyMetrics?.engagement) || 75,
        retention: Number(json.businessModel?.keyMetrics?.retention) || 85
      },
      personaRecommendations: json.personaRecommendations || 'Target personas need further analysis.',
      onboardingSuggestions: json.onboardingSuggestions || 'Onboarding process requires optimization.',
      integrationOpportunities: json.integrationOpportunities || 'Integration opportunities available.',
      auditingSecurityCompliance: json.auditingSecurityCompliance || 'Security measures should be implemented.',
      multiTenancyAssessment: json.multiTenancyAssessment || 'Multi-tenancy architecture recommended.',
      customerJourney: Array.isArray(json.customerJourney) ? json.customerJourney : ['Discovery', 'Trial', 'Conversion', 'Retention'],
      actionableRecommendations: json.actionableRecommendations || 'Further analysis recommended.'
    };
  } catch (error) {
    console.error('Gemini API Error:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to analyze SaaS description');
  }
};

/**
 * How to update/change Gemini API key:
 *   - Open `.env`
 *   - Set/update REACT_APP_GEMINI_API_KEY=your-key-here
 *   - Restart your dev server.
 *   - Never commit secrets to code repository.
 */
