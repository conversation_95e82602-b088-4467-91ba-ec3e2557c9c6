import { GoogleGenerative<PERSON><PERSON>, HarmBlockThreshold, HarmCategory } from '@google/generative-ai';
import type { SaaSAnalysisInput, SaaSAnalysisResult } from '../../types/saas';
import { getGeminiApiKey } from '../apiKeyStorage';

// Initialize Google Generative AI with safety settings and optimized parameters
function initializeGemini() {
  const apiKey = getGeminiApiKey();
  if (!apiKey) {
    throw new Error('Gemini API key not found. Please set VITE_GEMINI_API_KEY in environment or save key in settings.');
  }

  const genAI = new GoogleGenerativeAI(apiKey);
  
  // Configure model with parameters to minimize hallucination
  const model = genAI.getGenerativeModel({
    model: "gemini-1.5-flash",
    generationConfig: {
      temperature: 0.1, // Very low temperature for factual, consistent responses
      topK: 1, // Only consider the most likely token
      topP: 0.1, // Very focused sampling
      maxOutputTokens: 8192,
      responseMimeType: "application/json", // Force JSON response
    },
    safetySettings: [
      {
        category: HarmCategory.HARM_CATEGORY_HARASSMENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
      {
        category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
        threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
      },
    ],
  });

  return model;
}

export const analyzeSaaSWithGemini = async (
  input: SaaSAnalysisInput
): Promise<SaaSAnalysisResult> => {
  try {
    const model = initializeGemini();
    
    const prompt = `You are a senior SaaS business analyst and venture capital expert. Analyze the following SaaS description and provide comprehensive business intelligence. Return ONLY a valid JSON object with the exact structure below. Do not include any explanatory text before or after the JSON.

SaaS Description: ${input.description}

Provide a thorough analysis covering all aspects of the business opportunity. Return this exact JSON structure with realistic, data-driven insights:

{
  "executiveSummary": "Comprehensive 3-4 sentence executive summary",
  "overallScore": 75,
  "recommendation": "go",
  "problemSolution": {
    "problemStatement": "Clear problem definition",
    "problemSignificance": 8,
    "targetAudiencePainPoints": ["Pain point 1", "Pain point 2", "Pain point 3"],
    "proposedSolution": "Solution description",
    "uniqueValueProposition": "Unique value proposition",
    "existingAlternatives": ["Alternative 1", "Alternative 2"],
    "competitiveAdvantages": ["Advantage 1", "Advantage 2"]
  },
  "marketAnalysis": {
    "idealCustomerSegments": [
      {
        "name": "Primary Segment",
        "demographics": "Demographics description",
        "psychographics": "Psychographics description",
        "behaviors": ["Behavior 1", "Behavior 2"],
        "painPoints": ["Pain 1", "Pain 2"],
        "willingness_to_pay": 100
      }
    ],
    "totalAddressableMarket": {
      "value": 50000000000,
      "currency": "USD",
      "growthRate": 15
    },
    "serviceableAddressableMarket": {
      "value": 5000000000,
      "currency": "USD",
      "penetrationRate": 2
    },
    "marketResearchInsights": ["Insight 1", "Insight 2"],
    "customerValidationData": ["Validation 1", "Validation 2"]
  },
  "competitiveLandscape": {
    "competitors": [
      {
        "name": "Competitor 1",
        "type": "direct",
        "marketShare": 25,
        "strengths": ["Strength 1", "Strength 2"],
        "weaknesses": ["Weakness 1", "Weakness 2"],
        "pricingStrategy": "Pricing strategy",
        "keyFeatures": ["Feature 1", "Feature 2"]
      }
    ],
    "marketPositioning": "Market positioning strategy",
    "competitiveAdvantages": ["Advantage 1", "Advantage 2"],
    "threats": ["Threat 1", "Threat 2"],
    "opportunities": ["Opportunity 1", "Opportunity 2"]
  },
  "marketValidation": {
    "validationMethodologies": ["Method 1", "Method 2"],
    "quantitativeEvidence": [
      {
        "methodology": "Landing page test",
        "metric": "Conversion rate",
        "value": 3.5,
        "significance": "Strong interest"
      }
    ],
    "conversionRates": {
      "landingPage": 3.5,
      "signUp": 15,
      "trial": 25,
      "paid": 8
    },
    "demandSignals": ["Signal 1", "Signal 2"],
    "userFeedbackScores": {
      "problemFit": 8,
      "solutionFit": 7,
      "usability": 6,
      "value": 8
    }
  },
  "businessModel": {
    "pricingStrategy": "Freemium with tiered subscriptions",
    "pricingTiers": [
      {
        "name": "Basic",
        "price": 29,
        "features": ["Feature 1", "Feature 2"],
        "targetSegment": "Small businesses"
      }
    ],
    "revenueStreams": ["Subscriptions", "Premium features"],
    "financialProjections": {
      "year1": {
        "mrr": 50000,
        "arr": 600000,
        "customers": 500,
        "cac": 150,
        "ltv": 2400,
        "churnRate": 5,
        "grossMargin": 80,
        "burnRate": 100000,
        "engagement": 75,
        "retention": 85
      },
      "year2": {
        "mrr": 200000,
        "arr": 2400000,
        "customers": 2000,
        "cac": 120,
        "ltv": 3000,
        "churnRate": 4,
        "grossMargin": 82,
        "burnRate": 150000,
        "engagement": 78,
        "retention": 87
      },
      "year3": {
        "mrr": 500000,
        "arr": 6000000,
        "customers": 5000,
        "cac": 100,
        "ltv": 3600,
        "churnRate": 3,
        "grossMargin": 85,
        "burnRate": 200000,
        "engagement": 80,
        "retention": 90
      }
    },
    "keyMetrics": {
      "mrr": 50000,
      "arr": 600000,
      "customers": 500,
      "cac": 150,
      "ltv": 2400,
      "churnRate": 5,
      "grossMargin": 80,
      "burnRate": 100000,
      "engagement": 75,
      "retention": 85
    }
  },
  "riskAssessment": {
    "riskFactors": [
      {
        "category": "market",
        "description": "Market adoption risk",
        "probability": 6,
        "impact": 8,
        "mitigation": "Mitigation strategy"
      }
    ],
    "founderMarketFit": {
      "domainExpertise": 7,
      "technicalCapability": 8,
      "businessExperience": 6,
      "resourceAvailability": 7,
      "networkStrength": 6,
      "overallScore": 6.8
    },
    "swotAnalysis": {
      "strengths": ["Strength 1", "Strength 2"],
      "weaknesses": ["Weakness 1", "Weakness 2"],
      "opportunities": ["Opportunity 1", "Opportunity 2"],
      "threats": ["Threat 1", "Threat 2"]
    },
    "overallRiskScore": 6
  },
  "strategicRecommendations": {
    "recommendation": "go",
    "confidence": 8,
    "rationale": ["Reason 1", "Reason 2"],
    "validationExperiments": [
      {
        "name": "Landing page test",
        "description": "Test description",
        "timeline": "2 weeks",
        "budget": 5000,
        "successMetrics": ["Metric 1", "Metric 2"],
        "priority": "high"
      }
    ],
    "mvpPriorities": ["Priority 1", "Priority 2"],
    "successMetrics": ["Metric 1", "Metric 2"],
    "milestones": [
      {
        "name": "MVP Launch",
        "description": "Launch description",
        "timeline": "3 months",
        "dependencies": ["Dependency 1"],
        "successCriteria": ["Criteria 1"]
      }
    ],
    "nextSteps": ["Step 1", "Step 2"]
  },
  "securityCompliance": {
    "requirements": ["GDPR", "SOC 2"],
    "frameworks": ["ISO 27001"],
    "certifications": ["SOC 2 Type II"],
    "implementationCost": 50000,
    "timeline": "6 months"
  },
  "technicalArchitecture": {
    "scalabilityAssessment": "High scalability potential",
    "technologyStack": ["React", "Node.js", "PostgreSQL"],
    "infrastructureRequirements": ["Cloud hosting", "CDN"],
    "developmentComplexity": 7,
    "maintenanceCost": 20000
  },
  "marketTiming": {
    "marketMaturity": "Growing",
    "trendAnalysis": ["Trend 1", "Trend 2"],
    "timingScore": 8,
    "marketReadiness": "Ready"
  },
  "personaRecommendations": "Target persona recommendations",
  "onboardingSuggestions": "Onboarding and UX suggestions",
  "integrationOpportunities": "Integration opportunities",
  "auditingSecurityCompliance": "Security and compliance recommendations",
  "multiTenancyAssessment": "Multi-tenancy assessment",
  "customerJourney": ["Discovery", "Trial", "Conversion", "Retention"],
  "actionableRecommendations": "Actionable recommendations"
}

Ensure all numerical values are realistic and based on industry standards. Provide specific, actionable insights rather than generic statements.`;

    // Generate content using the official SDK
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    if (!text) {
      throw new Error('Empty response from Gemini API');
    }

    // Parse the JSON response
    let analysisData: any;
    try {
      // Clean the response text to extract JSON
      let cleanedText = text.trim();
      
      // Remove any markdown code blocks
      cleanedText = cleanedText.replace(/```json\s*/g, '').replace(/```\s*/g, '');
      
      // Find JSON object boundaries
      const jsonStart = cleanedText.indexOf('{');
      const jsonEnd = cleanedText.lastIndexOf('}') + 1;
      
      if (jsonStart === -1 || jsonEnd === 0) {
        throw new Error('No valid JSON found in Gemini response');
      }
      
      const jsonString = cleanedText.substring(jsonStart, jsonEnd);
      analysisData = JSON.parse(jsonString);
    } catch (parseError) {
      console.error('Failed to parse JSON:', text);
      throw new Error('Invalid JSON format in Gemini response');
    }

    // Map Gemini response to our comprehensive SaaSAnalysisResult
    return {
      executiveSummary: analysisData.executiveSummary || 'Analysis completed successfully.',
      overallScore: Number(analysisData.overallScore) || 75,
      recommendation: analysisData.recommendation || 'go',

      // Core Analysis Sections
      problemSolution: analysisData.problemSolution || {
        problemStatement: 'Problem analysis needed',
        problemSignificance: 7,
        targetAudiencePainPoints: ['Pain point analysis required'],
        proposedSolution: 'Solution analysis needed',
        uniqueValueProposition: 'Value proposition analysis needed',
        existingAlternatives: ['Alternative analysis required'],
        competitiveAdvantages: ['Advantage analysis required']
      },
