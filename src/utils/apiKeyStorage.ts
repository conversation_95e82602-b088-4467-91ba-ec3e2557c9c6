import Cookies from 'js-cookie';

const API_KEY_COOKIE = 'gemini_api_key';
const COOKIE_EXPIRES_DAYS = 30;

/**
 * Get the Gemini API key from environment or cookies
 * Priority: Cookie > Environment Variable
 */
export function getGeminiApiKey(): string | null {
  // First check cookies (user preference for cookie-based storage)
  const cookieKey = Cookies.get(API_KEY_COOKIE);
  if (cookieKey && cookieKey.trim()) {
    return cookieKey.trim();
  }

  // Fallback to environment variable
  const envKey = import.meta.env.VITE_GEMINI_API_KEY;
  if (envKey && envKey.trim() && envKey !== 'your_gemini_api_key_here') {
    return envKey.trim();
  }

  return null;
}

/**
 * Save API key to cookies
 */
export function saveGeminiApiKey(apiKey: string): void {
  if (!apiKey || !apiKey.trim()) {
    throw new Error('API key cannot be empty');
  }

  Cookies.set(API_KEY_COOKIE, apiKey.trim(), {
    expires: COOKIE_EXPIRES_DAYS,
    secure: window.location.protocol === 'https:',
    sameSite: 'strict'
  });
}

/**
 * Remove API key from cookies
 */
export function removeGeminiApiKey(): void {
  Cookies.remove(API_KEY_COOKIE);
}

/**
 * Check if API key is available
 */
export function hasGeminiApiKey(): boolean {
  return getGeminiApiKey() !== null;
}

/**
 * Validate API key format (basic validation)
 */
export function validateApiKey(apiKey: string): boolean {
  if (!apiKey || typeof apiKey !== 'string') {
    return false;
  }

  const trimmed = apiKey.trim();
  
  // Basic Google API key format validation
  // Google API keys typically start with "AIza" and are 39 characters long
  return trimmed.startsWith('AIza') && trimmed.length === 39;
}
