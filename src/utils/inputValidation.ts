import { Filter } from 'bad-words';

// Initialize profanity filter
const filter = new Filter();

// Business/SaaS related keywords for validation
const BUSINESS_KEYWORDS = [
  // Business models
  'saas', 'software', 'service', 'platform', 'solution', 'tool', 'app', 'application',
  'system', 'dashboard', 'analytics', 'automation', 'workflow', 'management',
  
  // Business terms
  'business', 'company', 'startup', 'enterprise', 'customer', 'client', 'user',
  'market', 'industry', 'revenue', 'subscription', 'pricing', 'monetization',
  
  // Technology terms
  'cloud', 'api', 'integration', 'database', 'security', 'scalable', 'mobile',
  'web', 'digital', 'online', 'remote', 'virtual', 'ai', 'ml', 'artificial intelligence',
  
  // Business functions
  'crm', 'erp', 'hr', 'finance', 'accounting', 'marketing', 'sales', 'support',
  'communication', 'collaboration', 'productivity', 'efficiency', 'optimization',
  
  // Target markets
  'b2b', 'b2c', 'enterprise', 'sme', 'small business', 'medium business',
  'freelancer', 'agency', 'consultant', 'team', 'organization'
];

// Validation constants
export const VALIDATION_RULES = {
  MIN_LENGTH: 20,
  MAX_LENGTH: 2000,
  MIN_BUSINESS_KEYWORDS: 1,
  RECOMMENDED_LENGTH: 100
};

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number; // 0-100
  suggestions: string[];
}

// Character count validation
export const validateLength = (text: string): { isValid: boolean; message?: string } => {
  const length = text.trim().length;
  
  if (length < VALIDATION_RULES.MIN_LENGTH) {
    return {
      isValid: false,
      message: `Description too short. Please provide at least ${VALIDATION_RULES.MIN_LENGTH} characters.`
    };
  }
  
  if (length > VALIDATION_RULES.MAX_LENGTH) {
    return {
      isValid: false,
      message: `Description too long. Please keep it under ${VALIDATION_RULES.MAX_LENGTH} characters.`
    };
  }
  
  return { isValid: true };
};

// Profanity validation
export const validateProfanity = (text: string): { isValid: boolean; message?: string } => {
  if (filter.isProfane(text)) {
    return {
      isValid: false,
      message: 'Please keep your description professional and appropriate.'
    };
  }
  
  return { isValid: true };
};

// Business terminology validation
export const validateBusinessTerms = (text: string): { 
  isValid: boolean; 
  message?: string; 
  foundKeywords: string[];
  score: number;
} => {
  const lowerText = text.toLowerCase();
  const foundKeywords = BUSINESS_KEYWORDS.filter(keyword => 
    lowerText.includes(keyword.toLowerCase())
  );
  
  const score = Math.min(100, (foundKeywords.length / VALIDATION_RULES.MIN_BUSINESS_KEYWORDS) * 100);
  
  if (foundKeywords.length < VALIDATION_RULES.MIN_BUSINESS_KEYWORDS) {
    return {
      isValid: false,
      message: 'Please include more business or SaaS-related terms to help us provide better analysis.',
      foundKeywords,
      score
    };
  }
  
  return { 
    isValid: true, 
    foundKeywords,
    score
  };
};

// Comprehensive validation
export const validateInput = (text: string): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];
  
  // Length validation
  const lengthResult = validateLength(text);
  if (!lengthResult.isValid && lengthResult.message) {
    errors.push(lengthResult.message);
  }
  
  // Profanity validation
  const profanityResult = validateProfanity(text);
  if (!profanityResult.isValid && profanityResult.message) {
    errors.push(profanityResult.message);
  }
  
  // Business terms validation
  const businessResult = validateBusinessTerms(text);
  if (!businessResult.isValid && businessResult.message) {
    warnings.push(businessResult.message);
  }
  
  // Length recommendations
  const length = text.trim().length;
  if (length < VALIDATION_RULES.RECOMMENDED_LENGTH && length >= VALIDATION_RULES.MIN_LENGTH) {
    warnings.push('Consider adding more details for a more comprehensive analysis.');
  }
  
  // Suggestions based on missing elements
  if (businessResult.foundKeywords.length === 0) {
    suggestions.push('Try including terms like: target audience, features, pricing model, or business goals.');
  }
  
  if (!text.toLowerCase().includes('target') && !text.toLowerCase().includes('audience') && !text.toLowerCase().includes('customer')) {
    suggestions.push('Consider describing your target audience or customers.');
  }
  
  if (!text.toLowerCase().includes('feature') && !text.toLowerCase().includes('function') && !text.toLowerCase().includes('capability')) {
    suggestions.push('Describe the key features or capabilities of your SaaS.');
  }
  
  // Calculate overall score
  let score = 0;
  if (lengthResult.isValid) score += 30;
  if (profanityResult.isValid) score += 20;
  score += Math.min(50, businessResult.score * 0.5);
  
  const isValid = errors.length === 0;
  
  return {
    isValid,
    errors,
    warnings,
    score: Math.round(score),
    suggestions
  };
};

// Get character count info
export const getCharacterInfo = (text: string) => {
  const length = text.length;
  const remaining = VALIDATION_RULES.MAX_LENGTH - length;
  const progress = (length / VALIDATION_RULES.MAX_LENGTH) * 100;
  
  let status: 'good' | 'warning' | 'error' = 'good';
  if (length < VALIDATION_RULES.MIN_LENGTH) status = 'error';
  else if (length > VALIDATION_RULES.MAX_LENGTH * 0.9) status = 'warning';
  
  return {
    length,
    remaining,
    progress,
    status,
    isMinMet: length >= VALIDATION_RULES.MIN_LENGTH,
    isMaxExceeded: length > VALIDATION_RULES.MAX_LENGTH
  };
};
