import LoadingPage from '@/pages/common/LoadingPage';
import ForbiddenPage from '@/pages/error/ForbiddenPage';
import NotFoundPage from '@/pages/error/NotFoundPage';
import React, { Suspense, memo } from 'react';
import { Route, Routes } from 'react-router-dom';
import Index from './pages/Index';
import HistoryPage from './pages/history/HistoryPage';

const AppRoutes: React.FC = memo(() => (
  <Suspense fallback={<LoadingPage message="Loading SaaScan..." />}>
    <Routes>
      <Route path="/" element={<Index />} />
      <Route path="/history" element={<HistoryPage />} />
      <Route path="/settings" element={<SettingsPage />} />
      <Route path="/403" element={<ForbiddenPage />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  </Suspense>
));

export default AppRoutes;
