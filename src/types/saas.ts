export interface SaaSAnalysisInput {
  description: string;
}

// Problem & Solution Analysis
export interface ProblemSolutionAnalysis {
  problemStatement: string;
  problemSignificance: number; // 1-10 scale
  targetAudiencePainPoints: string[];
  proposedSolution: string;
  uniqueValueProposition: string;
  existingAlternatives: string[];
  competitiveAdvantages: string[];
}

// Target Market & Customer Profile
export interface CustomerSegment {
  name: string;
  demographics: string;
  psychographics: string;
  behaviors: string[];
  painPoints: string[];
  willingness_to_pay: number;
}

export interface MarketAnalysis {
  idealCustomerSegments: CustomerSegment[];
  totalAddressableMarket: {
    value: number;
    currency: string;
    growthRate: number;
  };
  serviceableAddressableMarket: {
    value: number;
    currency: string;
    penetrationRate: number;
  };
  marketResearchInsights: string[];
  customerValidationData: string[];
}

// Competitive Landscape
export interface Competitor {
  name: string;
  type: 'direct' | 'indirect';
  marketShare: number;
  strengths: string[];
  weaknesses: string[];
  pricingStrategy: string;
  keyFeatures: string[];
}

export interface CompetitiveLandscape {
  competitors: Competitor[];
  marketPositioning: string;
  competitiveAdvantages: string[];
  threats: string[];
  opportunities: string[];
}

// Market Validation
export interface ValidationEvidence {
  methodology: string;
  metric: string;
  value: number;
  significance: string;
}

export interface MarketValidation {
  validationMethodologies: string[];
  quantitativeEvidence: ValidationEvidence[];
  conversionRates: {
    landingPage: number;
    signUp: number;
    trial: number;
    paid: number;
  };
  demandSignals: string[];
  userFeedbackScores: {
    problemFit: number;
    solutionFit: number;
    usability: number;
    value: number;
  };
}

// Business Model & Financial Projections
export interface PricingTier {
  name: string;
  price: number;
  features: string[];
  targetSegment: string;
}

export interface FinancialMetrics {
  mrr: number;         // Monthly Recurring Revenue
  arr: number;         // Annual Recurring Revenue
  customers: number;
  cac: number;         // Customer Acquisition Cost
  ltv: number;         // Customer Lifetime Value
  churnRate: number;   // % per month
  grossMargin: number; // %
  burnRate: number;    // Monthly burn
  engagement: number;  // e.g., DAU/WAU
  retention: number;   // %
}

export interface FinancialProjections {
  year1: FinancialMetrics;
  year2: FinancialMetrics;
  year3: FinancialMetrics;
  year5: FinancialMetrics;
}

export interface BusinessModel {
  pricingStrategy: string;
  pricingTiers: PricingTier[];
  revenueStreams: string[];
  financialProjections: FinancialProjections;
  keyMetrics: FinancialMetrics;
  industryBenchmarks: {
    churnRate: { value: number; percentile: number };
    cac: { value: number; percentile: number };
    ltv: { value: number; percentile: number };
    grossMargin: { value: number; percentile: number };
  };
}

// Risk Assessment
export interface RiskFactor {
  category: 'technical' | 'market' | 'financial' | 'competitive' | 'operational';
  description: string;
  probability: number; // 1-10
  impact: number; // 1-10
  mitigation: string;
}

export interface FounderMarketFit {
  domainExpertise: number; // 1-10
  technicalCapability: number; // 1-10
  businessExperience: number; // 1-10
  resourceAvailability: number; // 1-10
  networkStrength: number; // 1-10
  overallScore: number;
}

export interface SWOTAnalysis {
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
  threats: string[];
}

export interface RiskAssessment {
  riskFactors: RiskFactor[];
  founderMarketFit: FounderMarketFit;
  swotAnalysis: SWOTAnalysis;
  overallRiskScore: number; // 1-10
}

// Strategic Recommendations
export interface ValidationExperiment {
  name: string;
  description: string;
  timeline: string;
  budget: number;
  successMetrics: string[];
  priority: 'high' | 'medium' | 'low';
}

export interface Milestone {
  name: string;
  description: string;
  timeline: string;
  dependencies: string[];
  successCriteria: string[];
}

export interface StrategicRecommendations {
  recommendation: 'go' | 'no-go' | 'pivot';
  confidence: number; // 1-10
  rationale: string[];
  validationExperiments: ValidationExperiment[];
  mvpPriorities: string[];
  successMetrics: string[];
  milestones: Milestone[];
  nextSteps: string[];
}

// Additional Analysis Components
export interface SecurityCompliance {
  requirements: string[];
  frameworks: string[];
  certifications: string[];
  implementationCost: number;
  timeline: string;
}

export interface TechnicalArchitecture {
  scalabilityAssessment: string;
  technologyStack: string[];
  infrastructureRequirements: string[];
  developmentComplexity: number; // 1-10
  maintenanceCost: number;
}

export interface MarketTiming {
  marketMaturity: string;
  trendAnalysis: string[];
  timingScore: number; // 1-10
  marketReadiness: string;
}

// Main Analysis Result Interface
export interface SaaSAnalysisResult {
  executiveSummary: string;
  overallScore: number; // 1-100
  recommendation: 'go' | 'no-go' | 'pivot';

  // Core Analysis Sections
  problemSolution: ProblemSolutionAnalysis;
  marketAnalysis: MarketAnalysis;
  competitiveLandscape: CompetitiveLandscape;
  marketValidation: MarketValidation;
  businessModel: BusinessModel;
  riskAssessment: RiskAssessment;
  strategicRecommendations: StrategicRecommendations;

  // Additional Components
  securityCompliance: SecurityCompliance;
  technicalArchitecture: TechnicalArchitecture;
  marketTiming: MarketTiming;

  // Legacy fields for backward compatibility
  keyMetrics: FinancialMetrics;
  personaRecommendations: string;
  onboardingSuggestions: string;
  integrationOpportunities: string;
  auditingSecurityCompliance: string;
  multiTenancyAssessment: string;
  customerJourney: string[];
  actionableRecommendations: string;
}
