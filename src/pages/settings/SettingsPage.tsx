import React from 'react';
import Layout from '@/components/layout/Layout';
import SEOHead from '@/components/seo/SEOHead';
import { ApiKeySettings } from '@/components/settings/ApiKeySettings';
import { Settings } from 'lucide-react';

export default function SettingsPage() {
  return (
    <Layout>
      <SEOHead
        title="Settings - SaaScan"
        description="Configure your SaaScan settings including API keys and preferences."
        keywords="settings, API key, configuration, SaaScan"
        url="/settings"
      />
      <div className="bg-[var(--background)] text-[var(--headline)] min-h-screen">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-4">
              <Settings className="h-8 w-8 text-[var(--primary)]" />
              <h1 className="text-3xl font-bold text-[var(--headline)]">Settings</h1>
            </div>
            <p className="text-[var(--paragraph)] text-lg">
              Configure your SaaScan preferences and API settings.
            </p>
          </div>

          {/* Settings Content */}
          <div className="max-w-4xl">
            <div className="grid gap-8">
              {/* API Key Settings Section */}
              <section>
                <h2 className="text-xl font-semibold text-[var(--headline)] mb-4">
                  API Configuration
                </h2>
                <ApiKeySettings />
              </section>

              {/* Future settings sections can be added here */}
              {/* 
              <section>
                <h2 className="text-xl font-semibold text-[var(--headline)] mb-4">
                  Preferences
                </h2>
                <Card>
                  <CardContent className="p-6">
                    <p className="text-[var(--paragraph)]">
                      Additional preference settings will be available here in future updates.
                    </p>
                  </CardContent>
                </Card>
              </section>
              */}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
