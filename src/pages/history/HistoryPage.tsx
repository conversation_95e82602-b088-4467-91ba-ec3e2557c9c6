import { useCookieConsent } from '@/components/common/CookieConsent';
import Layout from '@/components/layout/Layout';
import SEOHead from '@/components/seo/SEOHead';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { useAnalysisStorage } from '@/hooks/useAnalysisStorage';
import type { SortCriteria, SortOrder, StoredAnalysis } from '@/utils/cookieStorage';
import { AnimatePresence, motion } from 'framer-motion';
import {
    AlertCircle,
    Calendar,
    Download,
    Eye,
    FileText,
    Search,
    SortAsc,
    SortDesc,
    Trash2
} from 'lucide-react';
import React, { useState } from 'react';

export default function HistoryPage() {
  const {
    analyses,
    loading,
    searchQuery,
    sortCriteria,
    sortOrder,
    updateSearch,
    updateSort,
    remove,
    removeMultiple,
    clearAll,
    isStorageEnabled,
    hasAnalyses
  } = useAnalysisStorage();

  const { isAccepted } = useCookieConsent();
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [expandedId, setExpandedId] = useState<string | null>(null);

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateSearch(e.target.value);
  };

  // Handle sort change
  const handleSortChange = (criteria: SortCriteria) => {
    const newOrder: SortOrder =
      sortCriteria === criteria && sortOrder === 'desc' ? 'asc' : 'desc';
    updateSort(criteria, newOrder);
  };

  // Handle individual selection
  const handleSelectAnalysis = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedIds(prev => [...prev, id]);
    } else {
      setSelectedIds(prev => prev.filter(selectedId => selectedId !== id));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(analyses.map(analysis => analysis.id));
    } else {
      setSelectedIds([]);
    }
  };

  // Handle delete selected
  const handleDeleteSelected = async () => {
    if (selectedIds.length === 0) return;

    const success = await removeMultiple(selectedIds);
    if (success) {
      setSelectedIds([]);
    }
  };

  // Handle delete single
  const handleDeleteSingle = async (id: string) => {
    await remove(id);
    setSelectedIds(prev => prev.filter(selectedId => selectedId !== id));
  };

  // Handle clear all
  const handleClearAll = async () => {
    const success = await clearAll();
    if (success) {
      setSelectedIds([]);
    }
  };

  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Export analysis as JSON
  const exportAnalysis = (analysis: StoredAnalysis) => {
    const dataStr = JSON.stringify(analysis, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `saas-analysis-${analysis.id}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  if (!isAccepted) {
    return (
      <Layout>
        <div className="min-h-[80vh] bg-[var(--background)] text-[var(--headline)] flex items-center justify-center p-4">
          <motion.div
            className="max-w-2xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="bg-[var(--card)] border-[var(--card-border)] shadow-xl p-8">
              <CardContent className="space-y-6">
                <div className="flex justify-center">
                  <div className="p-4 bg-[var(--highlight)] bg-opacity-10 rounded-full">
                    <AlertCircle className="h-12 w-12 text-[var(--highlight)]" />
                  </div>
                </div>

                <div className="space-y-3">
                  <h1 className="text-3xl font-bold text-[var(--card-headline)]">
                    Enable History Tracking
                  </h1>
                  <p className="text-lg text-[var(--card-paragraph)]">
                    To view and manage your SaaS analysis history, we need your permission to store data locally.
                  </p>
                </div>

                <div className="bg-[var(--button-secondary)] bg-opacity-50 rounded-lg p-4 space-y-2">
                  <h3 className="font-semibold text-[var(--card-headline)] flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Privacy First
                  </h3>
                  <ul className="text-sm text-[var(--card-paragraph)] space-y-1 text-left">
                    <li>• All data is stored locally on your device</li>
                    <li>• No personal information is sent to external servers</li>
                    <li>• You can delete your data at any time</li>
                    <li>• No tracking or analytics cookies</li>
                  </ul>
                </div>

                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button
                    onClick={() => {
                      localStorage.setItem('saascan_cookie_consent', 'accepted');
                      window.location.reload();
                    }}
                    className="bg-[var(--button)] text-[var(--button-text)] hover:bg-[var(--button-hover)] px-8 py-3 text-lg font-medium"
                  >
                    Enable History Tracking
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => window.location.href = '/'}
                    className="bg-[var(--button-secondary)] text-[var(--button-secondary-text)] hover:bg-[var(--button-secondary-hover)] border-[var(--border)] px-6 py-3"
                  >
                    Go Back to Analyzer
                  </Button>
                </div>

                <p className="text-xs text-[var(--secondary)] mt-4">
                  By enabling history tracking, you agree to store analysis results locally for your convenience.
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <SEOHead
        title="Analysis History - SaaScan"
        description="View and manage your saved SaaS analysis results. Search, sort, and export your AI-powered business insights."
        keywords="SaaS analysis history, business analysis results, saved analyses, SaaS metrics history"
        url="/history"
      />
      <div className="bg-[var(--background)] text-[var(--headline)] p-4 md:p-8">
        <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Analysis History</h1>
          <p className="text-[var(--paragraph)]">
            View and manage your saved SaaS analysis results
          </p>
        </div>

        {/* Controls */}
        <div className="mb-6 space-y-4">
          {/* Search and Sort */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--secondary)]" />
              <Input
                placeholder="Search analyses..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="pl-10 bg-[var(--card)] border-[var(--border)] text-[var(--card-headline)]"
              />
            </div>
            <div className="flex gap-2">
              <Select value={sortCriteria} onValueChange={(value: SortCriteria) => handleSortChange(value)}>
                <SelectTrigger className="w-40 bg-[var(--card)] border-[var(--border)]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Date
                    </div>
                  </SelectItem>
                  <SelectItem value="title">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Title
                    </div>
                  </SelectItem>
                  <SelectItem value="description">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Description
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleSortChange(sortCriteria)}
                className="bg-[var(--card)] border-[var(--border)] hover:bg-[var(--button-secondary)]"
              >
                {sortOrder === 'desc' ? <SortDesc className="h-4 w-4" /> : <SortAsc className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          {/* Bulk Actions */}
          {hasAnalyses && (
            <div className="flex items-center justify-between p-4 bg-[var(--card)] rounded-lg border border-[var(--border)]">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Checkbox
                    checked={selectedIds.length === analyses.length && analyses.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm text-[var(--paragraph)]">
                    {selectedIds.length > 0
                      ? `${selectedIds.length} selected`
                      : `Select all (${analyses.length})`
                    }
                  </span>
                </div>
              </div>
              <div className="flex gap-2">
                {selectedIds.length > 0 && (
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDeleteSelected}
                    className="bg-[var(--danger)] text-[var(--danger-text)] hover:bg-[var(--danger-hover)]"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Selected
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearAll}
                  className="bg-[var(--card)] border-[var(--border)] hover:bg-[var(--button-secondary)]"
                >
                  Clear All
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        {loading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="bg-[var(--card)] border-[var(--card-border)]">
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-3">
                    <div className="h-4 bg-[var(--button-secondary)] rounded w-1/3"></div>
                    <div className="h-3 bg-[var(--button-secondary)] rounded w-2/3"></div>
                    <div className="h-3 bg-[var(--button-secondary)] rounded w-1/2"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : !hasAnalyses ? (
          <div className="text-center py-16">
            <FileText className="h-16 w-16 text-[var(--secondary)] mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">No Analysis History</h2>
            <p className="text-[var(--paragraph)] mb-4">
              You haven't saved any SaaS analyses yet.
            </p>
            <Button
              onClick={() => window.location.href = '/'}
              className="bg-[var(--button)] text-[var(--button-text)] hover:bg-[var(--button-hover)]"
            >
              Start Analyzing
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <AnimatePresence>
              {analyses.map((analysis) => (
                <motion.div
                  key={analysis.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="bg-[var(--card)] border-[var(--card-border)] hover:shadow-lg transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          <Checkbox
                            checked={selectedIds.includes(analysis.id)}
                            onCheckedChange={(checked) =>
                              handleSelectAnalysis(analysis.id, checked as boolean)
                            }
                          />
                          <div className="flex-1 min-w-0">
                            <CardTitle className="text-lg text-[var(--card-headline)] mb-1">
                              {analysis.title}
                            </CardTitle>
                            <p className="text-sm text-[var(--card-paragraph)] line-clamp-2">
                              {analysis.input?.description || 'No description available'}
                            </p>
                            <div className="flex items-center gap-4 mt-2">
                              <Badge variant="secondary" className="bg-[var(--button-secondary)] text-[var(--button-secondary-text)]">
                                <Calendar className="h-3 w-3 mr-1" />
                                {formatDate(analysis.timestamp)}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setExpandedId(
                              expandedId === analysis.id ? null : analysis.id
                            )}
                            className="hover:bg-[var(--button-secondary)]"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => exportAnalysis(analysis)}
                            className="hover:bg-[var(--button-secondary)]"
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteSingle(analysis.id)}
                            className="hover:bg-[var(--danger)] hover:text-[var(--danger-text)]"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    <AnimatePresence>
                      {expandedId === analysis.id && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: 'auto', opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="overflow-hidden"
                        >
                          <CardContent className="pt-0">
                            <div className="border-t border-[var(--border)] pt-4">
                              <h4 className="font-semibold text-[var(--card-headline)] mb-2">
                                Executive Summary
                              </h4>
                              <p className="text-sm text-[var(--card-paragraph)] mb-4">
                                {analysis.result.executiveSummary}
                              </p>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <h5 className="font-medium text-[var(--card-headline)] mb-2">
                                    Key Metrics
                                  </h5>
                                  <div className="space-y-1 text-sm">
                                    <div className="flex justify-between">
                                      <span className="text-[var(--card-paragraph)]">Churn Rate:</span>
                                      <span className="text-[var(--card-headline)]">{analysis.result.keyMetrics.churnRate}%</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-[var(--card-paragraph)]">MRR:</span>
                                      <span className="text-[var(--card-headline)]">${analysis.result.keyMetrics.mrr}</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-[var(--card-paragraph)]">ARR:</span>
                                      <span className="text-[var(--card-headline)]">${analysis.result.keyMetrics.arr}</span>
                                    </div>
                                  </div>
                                </div>

                                <div>
                                  <h5 className="font-medium text-[var(--card-headline)] mb-2">
                                    Recommendations
                                  </h5>
                                  <p className="text-sm text-[var(--card-paragraph)] line-clamp-4">
                                    {analysis.result.actionableRecommendations}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        )}
        </div>
      </div>
    </Layout>
  );
}
