import { SaasAnalyzer } from '@/components/analysis/SaasAnalyzer';
import HeroSection from '@/components/layout/HeroSection';
import Layout from '@/components/layout/Layout';
import SEOHead from '@/components/seo/SEOHead';

export default function Index() {
  return (
    <Layout>
      <SEOHead
        title="SaaScan - AI-Powered SaaS Analysis Tool"
        description="Get comprehensive AI-powered analysis for your SaaS ideas. Market insights, key metrics, strategic recommendations, and business validation in seconds."
        keywords="SaaS analysis, AI business analysis, startup validation, market research, business metrics, SaaS metrics, entrepreneurship, business planning"
        url="/"
      />
      <div className="bg-[var(--background)] text-[var(--headline)]">
        <div className="container mx-auto px-4">
          <HeroSection />
          <div className="pb-16">
            <SaasAnalyzer />
          </div>
        </div>
      </div>
    </Layout>
  );
}
