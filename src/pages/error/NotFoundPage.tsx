import React from 'react';
import { motion } from 'framer-motion';
import { Home, ArrowLeft, Search } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Layout from '@/components/layout/Layout';
import Logo from '@/components/common/Logo';
import SEOHead from '@/components/seo/SEOHead';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Layout>
      <SEOHead
        title="Page Not Found - 404 Error"
        description="The page you're looking for doesn't exist. Return to SaaScan to analyze your SaaS ideas with AI-powered insights."
        noIndex={true}
      />
      
      <div className="min-h-[80vh] bg-[var(--background)] flex items-center justify-center p-4">
        <motion.div
          className="max-w-2xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-[var(--card)] border-[var(--card-border)] shadow-xl">
            <CardContent className="p-8 space-y-6">
              {/* Logo */}
              <div className="flex justify-center">
                <Logo size="lg" animated={true} linkTo={null} />
              </div>

              {/* 404 Animation */}
              <motion.div
                className="text-8xl font-bold text-[var(--highlight)] opacity-20"
                animate={{
                  scale: [1, 1.05, 1],
                  opacity: [0.2, 0.3, 0.2]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                404
              </motion.div>

              {/* Content */}
              <div className="space-y-4">
                <h1 className="text-3xl font-bold text-[var(--card-headline)]">
                  Page Not Found
                </h1>
                <p className="text-lg text-[var(--card-paragraph)]">
                  Oops! The page you're looking for seems to have wandered off into the digital void.
                </p>
                <p className="text-sm text-[var(--secondary)]">
                  Don't worry, even the best SaaS ideas sometimes take unexpected paths.
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
                <motion.div whileTap={{ scale: 0.98 }}>
                  <Button
                    onClick={() => navigate(-1)}
                    variant="outline"
                    className="bg-[var(--button-secondary)] text-[var(--button-secondary-text)] hover:bg-[var(--button-secondary-hover)] border-[var(--border)]"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Go Back
                  </Button>
                </motion.div>

                <motion.div whileTap={{ scale: 0.98 }}>
                  <Link to="/">
                    <Button className="bg-[var(--button)] text-[var(--button-text)] hover:bg-[var(--button-hover)]">
                      <Home className="h-4 w-4 mr-2" />
                      Back to Home
                    </Button>
                  </Link>
                </motion.div>

                <motion.div whileTap={{ scale: 0.98 }}>
                  <Link to="/history">
                    <Button
                      variant="outline"
                      className="bg-[var(--card)] text-[var(--card-headline)] hover:bg-[var(--button-secondary)] border-[var(--border)]"
                    >
                      <Search className="h-4 w-4 mr-2" />
                      View History
                    </Button>
                  </Link>
                </motion.div>
              </div>

              {/* Help Text */}
              <div className="pt-4 border-t border-[var(--border)]">
                <p className="text-xs text-[var(--secondary)]">
                  If you believe this is an error, please contact our support team.
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </Layout>
  );
};

export default NotFoundPage;
