import React from 'react';
import { motion } from 'framer-motion';
import { Home, ArrowLeft, Shield, Lock } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import Layout from '@/components/layout/Layout';
import Logo from '@/components/common/Logo';
import SEOHead from '@/components/seo/SEOHead';

const ForbiddenPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Layout>
      <SEOHead
        title="Access Forbidden - 403 Error"
        description="You don't have permission to access this resource. Return to SaaScan to continue analyzing your SaaS ideas."
        noIndex={true}
      />
      
      <div className="min-h-[80vh] bg-[var(--background)] flex items-center justify-center p-4">
        <motion.div
          className="max-w-2xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="bg-[var(--card)] border-[var(--card-border)] shadow-xl">
            <CardContent className="p-8 space-y-6">
              {/* Logo */}
              <div className="flex justify-center">
                <Logo size="lg" animated={true} linkTo={null} />
              </div>

              {/* Icon Animation */}
              <motion.div
                className="flex justify-center"
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <div className="p-6 bg-[var(--danger)] bg-opacity-10 rounded-full">
                  <Shield className="h-16 w-16 text-[var(--danger)]" />
                </div>
              </motion.div>

              {/* Content */}
              <div className="space-y-4">
                <h1 className="text-3xl font-bold text-[var(--card-headline)]">
                  Access Forbidden
                </h1>
                <p className="text-lg text-[var(--card-paragraph)]">
                  You don't have permission to access this resource.
                </p>
                <p className="text-sm text-[var(--secondary)]">
                  This area might be restricted or require special permissions.
                </p>
              </div>

              {/* 403 Badge */}
              <div className="flex justify-center">
                <div className="flex items-center gap-2 px-4 py-2 bg-[var(--danger)] bg-opacity-10 rounded-full">
                  <Lock className="h-4 w-4 text-[var(--danger)]" />
                  <span className="text-sm font-medium text-[var(--danger)]">Error 403</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center pt-4">
                <motion.div whileTap={{ scale: 0.98 }}>
                  <Button
                    onClick={() => navigate(-1)}
                    variant="outline"
                    className="bg-[var(--button-secondary)] text-[var(--button-secondary-text)] hover:bg-[var(--button-secondary-hover)] border-[var(--border)]"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Go Back
                  </Button>
                </motion.div>

                <motion.div whileTap={{ scale: 0.98 }}>
                  <Link to="/">
                    <Button className="bg-[var(--button)] text-[var(--button-text)] hover:bg-[var(--button-hover)]">
                      <Home className="h-4 w-4 mr-2" />
                      Back to Home
                    </Button>
                  </Link>
                </motion.div>
              </div>

              {/* Help Text */}
              <div className="pt-4 border-t border-[var(--border)]">
                <p className="text-xs text-[var(--secondary)]">
                  If you believe you should have access to this resource, please contact support.
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </Layout>
  );
};

export default ForbiddenPage;
