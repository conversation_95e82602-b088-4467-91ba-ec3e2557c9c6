import React from 'react';
import { motion } from 'framer-motion';
import { Loader2, Sparkles } from 'lucide-react';
import Layout from '@/components/layout/Layout';
import Logo from '@/components/common/Logo';

interface LoadingPageProps {
  message?: string;
  showLayout?: boolean;
}

const LoadingPage: React.FC<LoadingPageProps> = ({ 
  message = "Loading...", 
  showLayout = true 
}) => {
  const LoadingContent = () => (
    <div className="min-h-[80vh] bg-[var(--background)] flex items-center justify-center p-4">
      <motion.div
        className="text-center space-y-6"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Logo */}
        <motion.div
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.8, 1, 0.8]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <Logo size="lg" animated={true} linkTo={null} />
        </motion.div>

        {/* Loading Animation */}
        <div className="flex justify-center">
          <motion.div
            className="relative"
            animate={{ rotate: 360 }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "linear"
            }}
          >
            <Loader2 className="h-8 w-8 text-[var(--highlight)]" />
          </motion.div>
        </div>

        {/* Loading Message */}
        <motion.div
          className="space-y-2"
          animate={{
            opacity: [0.6, 1, 0.6]
          }}
          transition={{
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <h2 className="text-xl font-semibold text-[var(--headline)]">
            {message}
          </h2>
          <p className="text-sm text-[var(--secondary)]">
            Please wait while we prepare everything for you
          </p>
        </motion.div>

        {/* Animated Dots */}
        <div className="flex justify-center space-x-1">
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              className="w-2 h-2 bg-[var(--highlight)] rounded-full"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                delay: index * 0.2,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>

        {/* Sparkles Animation */}
        <div className="relative">
          {[...Array(5)].map((_, index) => (
            <motion.div
              key={index}
              className="absolute"
              style={{
                left: `${20 + index * 15}%`,
                top: `${Math.random() * 100}%`
              }}
              animate={{
                y: [-10, -20, -10],
                opacity: [0, 1, 0],
                scale: [0.5, 1, 0.5]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: index * 0.4,
                ease: "easeInOut"
              }}
            >
              <Sparkles className="h-4 w-4 text-[var(--highlight)]" />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );

  if (showLayout) {
    return (
      <Layout>
        <LoadingContent />
      </Layout>
    );
  }

  return <LoadingContent />;
};

export default LoadingPage;
